﻿namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 号源管理控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class NumberSourceController : BaseControllerAuthorize
{
    private readonly INumberSourceService _numberSourceService;

    /// <summary>
    /// 号源管理
    /// </summary>
    /// <param name="numberSourceService"></param>
    public NumberSourceController(INumberSourceService numberSourceService)
    {
        _numberSourceService = numberSourceService;
    }

    #region 时段管理

    /// <summary>
    /// 获取全部号源时段
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetTimeSlot")]
    [ActionPermission(ActionType.Query, "获取全部号源时段", "号源业务")]
    public async Task<TimeSlotEntryOutput[]> GetTimeSlot()
    {
        return await _numberSourceService.GetTimeSlotEntry();
    }

    /// <summary>
    /// 添加号源时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("AddTimeSlot")]
    [ActionPermission(ActionType.Button, "添加号源时段", "号源业务")]
    public async Task<bool> AddTimeSlot([FromBody] AddTimeSlotEntryInput input)
    {
        return await _numberSourceService.AddTimeSlotEntry(input);
    }

    /// <summary>
    /// 编辑号源时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("EditTimeSlot")]
    [ActionPermission(ActionType.Button, "编辑号源时段", "号源业务")]
    public async Task<bool> EditTimeSlot([FromBody] EditTimeSlotEntryInput input)
    {
        return await _numberSourceService.EditTimeSlotEntry(input);
    }

    /// <summary>
    /// 删除号源时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("DeleteTimeSlot")]
    [ActionPermission(ActionType.Button, "删除号源时段", "号源业务")]
    public async Task<bool> DeleteTimeSlot([FromBody] DeleteTimeSlotEntryInput input)
    {
        return await _numberSourceService.DeleteTimeSlotEntry(input);
    }

    #endregion

    #region 号源类型关联时段

    /// <summary>
    /// 获取全部号源类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetSourceType")]
    [ActionPermission(ActionType.Query, "获取全部号源类型", "号源业务")]
    public async Task<SourceTypeOutput[]> GetSourceType()
    {
        return await _numberSourceService.GetSourceType();
    }

    /// <summary>
    /// 获取指定类型的时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetTimeSlotByType")]
    [ActionPermission(ActionType.Query, "获取指定类型的时段", "号源业务")]
    public async Task<TimeSlotOutput[]> GetTimeSlotByType(TimeSlotInput input)
    {
        return await _numberSourceService.GetTimeSlotByType(input);
    }

    /// <summary>
    /// 编辑指定类型的号源时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("EditTimeSlotByType")]
    [ActionPermission(ActionType.Button, "编辑指定类型的号源时段", "号源业务")]
    public async Task<bool> EditTimeSlotByType([FromBody] EditTimeSlotByTypeInput input)
    {
        return await _numberSourceService.EditTimeSlotByType(input);
    }

    #endregion

    #region 号源管理
    [HttpPost("GetPersonType")]
    [ActionPermission(ActionType.Query, "个检类型查询", "号源业务")]
    public async Task<PersonNumberSourceOutput[]> GetPersonType()
    {
        return await _numberSourceService.GetPersonType();
    }

    /// <summary>
    /// 号源查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetNumberSource")]
    [ActionPermission(ActionType.Query, "号源查询", "号源业务")]
    public async Task<NumberSourceOutput[]> GetNumberSource([FromBody] NumberSourceInput input)
    {
        return await _numberSourceService.GetNumberSource(input);
    }

    /// <summary>
    /// 获取某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetNumberSourceByDate")]
    [ActionPermission(ActionType.Query, "获取某一天的号源", "号源业务")]
    public async Task<NumberSourceOutput[]> GetNumberSourceByDate([FromBody] NumberSourceByDayInput input)
    {
        return await _numberSourceService.GetNumberSourceByDate(input);
    }

    /// <summary>
    /// 编辑号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("EditNumberSource")]
    [ActionPermission(ActionType.Button, "编辑号源", "号源业务")]
    public async Task<bool> EditNumberSource([FromBody] List<EditNumberSourceInput> input)
    {
        return await _numberSourceService.EditNumberSource(input);
    }

    /// <summary>
    /// 删除号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("DeleteNumberSource")]
    [ActionPermission(ActionType.Button, "删除号源", "号源业务")]
    public async Task<bool> DeleteNumberSource([FromBody] DeleteNumberSourceInput input)
    {
        return await _numberSourceService.DeleteNumberSource(input);
    }

    /// <summary>
    /// 设置号源休假
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("SetNumberSourceVacation")]
    [ActionPermission(ActionType.Button, "设置号源休假", "号源业务")]
    public async Task<bool> SetNumberSourceVacation([FromBody] List<SetNumberSourceVacationInput> input)
    {
        return await _numberSourceService.SetNumberSourceVacation(input);
    }

    /// <summary>
    /// 获取单位补检信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetCompanyResidual")]
    [ActionPermission(ActionType.Query, "获取单位补检信息", "号源业务")]
    public async Task<CompanyResidualOutput> GetCompanyResidual([FromBody] CompanyResidualInput input)
    {
        return await _numberSourceService.GetCompanyResidual(input);
    }

    /// <summary>
    /// 编辑单位补检生效信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("EditCompanyResidual")]
    [ActionPermission(ActionType.Button, "编辑单位补检生效信息", "号源业务")]
    public async Task<bool> EditCompanyResidual([FromBody] EditCompanyResidualInput input)
    {
        return await _numberSourceService.EditCompanyResidual(input);
    }
    #endregion

}

