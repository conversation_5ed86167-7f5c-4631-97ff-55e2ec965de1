﻿using PeHubCoreNorm.Business.Input;

namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 订单控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class OrderController : BaseControllerRoleAuthorize
{
    private readonly IOrderService _orderService;

    public OrderController(IOrderService orderService)
    {
        _orderService = orderService;
    }

    /// <summary>
    /// 获取个检订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetPersonOrderList")]
    [ActionPermission(ActionType.Query, "获取个检订单列表", "订单业务")]
    public async Task<SqlSugarPagedList<OrderList>> GetPersonOrderList([FromBody] PersonOrderPageInput query)
    {
        return await _orderService.GetPersonOrderList(query);
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetOrderDetail")]
    [ActionPermission(ActionType.Query, "GetOrderDetail", "APP端用户业务")]
    public async Task<List<OrderDetail>> GetOrderDetail([FromBody] OrderDetailQuery query)
    {
        return await _orderService.GetOrderDetail(query);
    }


    /// <summary>
    /// 获取团检订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetTeamOrderList")]
    [ActionPermission(ActionType.Query, "获取个检订单列表", "订单业务")]
    public async Task<SqlSugarPagedList<TeamOrder>> GetTeamOrderList([FromBody] TeamOrderPageQueryInput query)
    {
        return await _orderService.GetTeamOrderList(query);
    }
    
}
