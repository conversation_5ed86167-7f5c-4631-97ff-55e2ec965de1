﻿using Microsoft.AspNetCore.Authorization;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
/// 客户信息管理控制器
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
public class MembersController : BaseControllerAuthorize
{
    private readonly IMembersService _membersService;

    public MembersController(IMembersService membersService)
    {
        _membersService = membersService;
    }

	/// <summary>
	///     登陆
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[AllowAnonymous]
	[HttpPost("authlogin")]
	[ActionPermission(ActionType.Button, "authlogin", "APP端用户业务")] //LoginIn用于日志分类
	public async Task<dynamic> authlogin(AuthFormInput input)
	{
		_membersService.ChangTenant(input.areaKey);
		return await _membersService.authlogin(input);
	}


	/// <summary>
	///     获取图片验证码
	/// </summary>
	/// <returns></returns>
	[HttpGet("getAuthCaptcha")]
	[AllowAnonymous]
	[IgnoreLog]
	public async Task<dynamic> getAuthCaptcha()
	{
		return await _membersService.GetCaptchaInfo();
	}

	/// <summary>
	///   获取用户信息
	/// </summary>
	/// <returns></returns>
	[HttpGet("getMemberInfo")]
	[ActionPermission(ActionType.Query, "登录时获取用户信息", "APP端用户业务")]
	public async Task<dynamic> getMemberInfo()
	{
		return null;
		//return await _membersService.getMemberInfo();
	}


	/// <summary>
	///   获取用户信息
	/// </summary>
	/// <returns></returns>
	[HttpGet("getMemberbyWId")]
	[ActionPermission(ActionType.Query, "wid获取用户信息", "APP端用户业务")]
	[AllowAnonymous]
	public async Task<dynamic> getMemberbyWId([FromQuery] MembersWIdInput input)
	{
		_membersService.ChangTenant(input.areaKey);
		return await _membersService.GetMembersByWId(input.wId,input.areaKey);
	}


    /// <summary>
	/// 获取用户id绑定的卡
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("GetCardsById")]
    [ActionPermission(ActionType.Query, "获取用户id绑定的卡")]
    public async Task<dynamic> GetCardsById()
    {
        return _membersService.GetCardsById(UserManager.UserId).Select(card => new {
			card.Id,
			card.CardType,
			card.CardNo,
			card.Name,
			card.Tel,
			card.Sex,
			card.BirthDate,
			card.Marital,
			card.PatientId,
            isDefault = card.IsDefault
        }).ToList();
	}

    /// <summary>
	/// 建卡或更新（默认绑定用户id）
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("EditCards")]
	[ActionPermission(ActionType.Button, "建卡或更新（默认绑定用户id）")]
    public async Task<dynamic> EditCards(CardsInput input)
    {
		CardList card = new CardList()
		{ 
			CardType = input.cardType,
			CardNo = input.cardNo,
			Tel = input.tel,
			Name = input.name,
			BirthDate = input.birthDate,
			Sex = input.sex,
            PatientId = input.patientId,
            Marital = input.marital,
		};
        //判断用户是否已超出绑卡次数
        var cards =  _membersService.GetCardsById(UserManager.UserId);
		//用于判断是否更新
		var cards_item = cards.FirstOrDefault(x => x.CardNo == card.CardNo && x.CardType == card.CardType && x.Name == card.Name);

        if (cards.Count > 10&& cards_item == null)
		{
			return Unify.SetError("绑定的卡不能超出10张。");
        }

        //查询该信息的卡是否存在，不存在则新建卡，存在则更新
        var editcard = await _membersService.EditCardsReturnEntity(card);
		var ismap= _membersService.MembersBingsCards(UserManager.UserId, editcard.Id);
		if (ismap==null)
		{
            return Unify.SetError("绑定的卡关系失败！");
		}
		//绑卡
		return editcard;
	}

    /// <summary>
	/// 修改卡的信息（Name、Tel、BirthDate、Sex、Marital、PatientId、CardType、CardNo）
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	/// <exception cref="Exception"></exception>
	[HttpPost("UpdateCardsInfo")]
    [ActionPermission(ActionType.Button, "修改卡的信息")]
    public async Task<dynamic> UpdateCardsInfo(UpdateCardsInfoInput input)
    {
        CardList card = new CardList()
        {
            CardType = input.cardType,
            CardNo = input.cardNo,
            Tel = input.tel,
            Name = input.name,
            BirthDate = input.birthDate,
            Sex = input.sex,
            Marital = input.marital,
            PatientId = input.patientId,
            Id =input.id,
        };

        //根据卡id获取卡信息
        var item =await _membersService.GetCardById(card.Id);
        if (item == null)
        {
            return Unify.SetError("更新失败:该卡不存在！");
        }
        //更新
        return _membersService.UpdateCard(card);
    }



    /// <summary>
	/// 用户id解绑卡
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("UnbindTheCardById")]
    [ActionPermission(ActionType.Button, "用户id解绑卡")]
    public async Task<dynamic> UnbindTheCardById(UnbindTheCardInput input)
    {
		return await _membersService.UnbindTheCard(UserManager.UserId, input.cardId);
    }

	/// <summary>
	///     第三方登陆
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[AllowAnonymous]
	[HttpPost("authloginOther")]
	[ActionPermission(ActionType.Button, "第三方授权登录", "APP端用户业务")] //LoginIn用于日志分类
	public async Task<dynamic> authloginOther(AuthFormInput input)
	{
		_membersService.ChangTenant(input.areaKey);
		return await _membersService.authloginOther(input);
	}


}
