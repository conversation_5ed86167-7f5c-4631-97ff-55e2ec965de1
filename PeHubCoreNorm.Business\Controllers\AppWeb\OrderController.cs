﻿using Mapster;
using PeHubCoreNorm.Business.Input;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
/// 订单业务
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
public class OrderController : BaseControllerAuthorize
{
    private readonly IOrderService _orderService;
    public OrderController(IOrderService orderService)
    {
        _orderService = orderService;
    }

    /// <summary>
    /// 添加个检订单
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    [HttpPost("AddPersonOrder")]
    [ActionPermission(ActionType.Button, "AddPersonOrder", "APP端用户业务")]
    public async Task<bool> AddPersonOrder([FromBody] PersonOrderInput order)
    {
        return await _orderService.AddPersonOrderProcess(order);
    }

    /// <summary>
    /// 取消个检订单
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    [HttpPost("CancelPersonOrder")]
    [ActionPermission(ActionType.Button, "CancelPersonOrder", "APP端用户业务")]
    public async Task<bool> CancelPersonOrder([FromBody] CancelOrder order)
    {
        return await _orderService.CancelPersonOrder(order);
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetOrderList")]
    [ActionPermission(ActionType.Query, "GetOrderList", "APP端用户业务")]
    public async Task<List<OrderList>> GetOrderList([FromBody] OrderQuery query)
    {
        return await _orderService.GetOrderList(query);
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetOrderDetail")]
    [ActionPermission(ActionType.Query, "GetOrderDetail", "APP端用户业务")]
    public async Task<List<OrderDetail>> GetOrderDetail([FromBody] OrderDetailQuery query)
    {
        return await _orderService.GetOrderDetail(query);
    }
}
