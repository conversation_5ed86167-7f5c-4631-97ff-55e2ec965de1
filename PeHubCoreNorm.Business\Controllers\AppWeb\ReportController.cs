﻿using Microsoft.AspNetCore.Authorization;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
///  报告业务
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
[AllowAnonymous]
public class ReportController : BaseControllerAuthorize
{
    private readonly IReportService _reportService;
    public ReportController(IReportService reportService)
    {
        _reportService = reportService;
    }

    /// <summary>
    /// 获取报告列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetReportList")]
    [ActionPermission(ActionType.Query, "报告列表查询", "APP端用户业务")]
    public async Task<List<ReportOutput>> GetReportList([FromBody] ReportInput input)
    {
        return await _reportService.GetReportList(input);
    }

    /// <summary>
    /// 获取报告详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetReportDetail")]
    [ActionPermission(ActionType.Query, "报告详情查询", "APP端用户业务")]
    public async Task<ReportDetailOutput> GetReportDetail([FromBody] ReportDetailInput input)
    {
        return await _reportService.GetReportDetail(input);
    }
}
