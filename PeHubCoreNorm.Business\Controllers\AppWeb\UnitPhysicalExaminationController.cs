﻿using Microsoft.AspNetCore.Authorization;
using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using PeHubCoreNorm.Business.Services.UnitPhysicalExamination;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Controllers.AppWeb
{

    [ApiExplorerSettings(GroupName = "AppWeb")]
    [Route("/AppWeb/[controller]")]
    [AllowAnonymous]
    public class UnitPhysicalExaminationController : BaseControllerAuthorize
    {
        private readonly IUnitPersonnelListService _unitPersonnelListService;
        private readonly IUnitTitlePageService _unitTitlePageService;
        private readonly IItemTemplateService _unitPhysicalExaminationService;
        private readonly IClusterCombService _clusterCombService;
        private readonly IOrderService _orderService;
        public UnitPhysicalExaminationController(IUnitPersonnelListService unitPersonnelListService, IUnitTitlePageService unitTitlePageService,IItemTemplateService unitPhysicalExaminationService,IClusterCombService clusterCombService,IOrderService orderService)
        {
            _unitPersonnelListService = unitPersonnelListService;
            _unitTitlePageService = unitTitlePageService;
            _unitPhysicalExaminationService= unitPhysicalExaminationService;
            _clusterCombService = clusterCombService;
            _orderService = orderService;
        }

        /// <summary>
        /// 验证单位人员信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("VerifyPersonInfo")]
        [ActionPermission(ActionType.Button, "VerifyPersonInfo", "APP端用户业务")]
        public async Task<List<UnitPersonnelList>> VerifyPersonInfo([FromBody] AuthenticationToUntiInput query)
        {
            return await _unitPersonnelListService.VerifyPersonInfo(query);
        }

        /// <summary>
        /// 根据单位编码和体检次数获取单位封面
        /// </summary>
        /// <param name="companyCode">单位编码</param>
        /// <param name="companyTimes">体检次数</param>
        /// <returns></returns>
        [HttpGet("GetUnitTitlePageByCompany")]
        [ActionPermission(ActionType.Query, "单位封面查询", "单位封面管理")]
        public async Task<UnitTitlePageOutput> GetUnitTitlePageByCompany([FromQuery] string companyCode, [FromQuery] int companyTimes)
        {
            return await _unitTitlePageService.GetUnitTitlePageByCompany(companyCode, companyTimes);
        }

        /// <summary>
        /// 获取单位人员的套餐列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetPackageInfo")]
        [ActionPermission(ActionType.Button, "GetPackageInfo", "APP端用户业务")]
        public async Task<List<UnitPersonnelList>> GetPackageInfo([FromBody] UnitPersonnelQueryInput input)
        {
         
              return await _unitPhysicalExaminationService.GetUnitPersonnelWithPackageInfo(input);
   
        }

        /// <summary>
        /// 获取单位套餐内的项目
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetCompanyClusterCombs")]
        [ActionPermission(ActionType.Button, "GetCompanyClusterCombs", "APP端用户业务")]
        public async Task<List<CompanyClusterDetailsOutput>> GetCompanyClusterCombs(MapClusterCombInput query)
        {
            return await _clusterCombService.GetCompanyClusterCombs(query);
        }

        /// <summary>
        /// 创建单位订单（需要登录）
        /// </summary>
        /// <param name="input">创建订单输入</param>
        /// <returns>创建的订单详情</returns>
        [HttpPost("CreateTeamOrderAuth")]
        public async Task<TeamOrder> CreateTeamOrderAuth([FromBody] AddTeamOrderInput input)
        {

            TeamOrder teamOrder = input.Adapt<AddTeamOrderInput, TeamOrder>();
            return await _orderService.CreateTeamOrder(teamOrder);
        }


        /// <summary>
        /// 取消单位订单（需要登录）
        /// </summary>
        /// <param name="input">取消单位订单</param>
        /// <returns>创建的订单详情</returns>
        [HttpPost("CanceleTeamOrder")]
        public async Task<bool> CanceleTeamOrder([FromBody] AddTeamOrderInput input)
        {
            TeamOrder teamOrder = input.Adapt<AddTeamOrderInput, TeamOrder>();
            return await _orderService.CanceleTeamOrder(teamOrder);
        }


        /// <summary>
        /// 改期单位订单（需要登录）
        /// </summary>
        /// <param name="input">创建订单输入</param>
        /// <returns>改期单位订单</returns>
        [HttpPost("RescheduleTeamOrder")]
        public async Task<bool> RescheduleTeamOrder([FromBody] AddTeamOrderInput input)
        {
            TeamOrder teamOrder = input.Adapt<AddTeamOrderInput, TeamOrder>();
            return await _orderService.RescheduleTeamOrder(teamOrder);
        }




    }
}
