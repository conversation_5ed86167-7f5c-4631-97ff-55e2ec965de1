﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 单位补检信息表
    /// </summary>
    [SugarTable("CompanyResidualSource", TableDescription = "单位补检信息表")]
    public class CompanyResidualSource : BaseEntity
    {
        /// <summary>
        /// 单位编码
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "CompanyCode", ColumnDescription = "单位编码", Length = 8)]
        public virtual string CompanyCode { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>

        [SugarColumn(IsNullable = false, ColumnName = "CompanyName", ColumnDescription = "单位名称", Length = 100)]
        public virtual string CompanyName { get; set; }

        /// <summary>
        /// 是否启用补检号源(默认不启用：F)
        /// </summary>
        [SugarColumn(ColumnName = "IsEnabled", ColumnDescription = "是否启用补检号源(默认不启用：F)")]
        public virtual string IsEnabled { get; set; } = "F";

        /// <summary>
        /// 补检号源生效开始日期
        /// </summary>
        [SugarColumn(ColumnName = "StartDate", ColumnDescription = "补检号源生效开始日期")]
        public virtual DateTime? StartDate { get; set; }

        /// <summary>
        /// 补检号源生效结束日期
        /// </summary>
        [SugarColumn(ColumnName = "EndDate", ColumnDescription = "补检号源生效结束日期")]
        public virtual DateTime? EndDate { get; set; }

        /// <summary>
        /// 补检号源启动日期
        /// </summary>
        [SugarColumn(ColumnName = "OpenDate", ColumnDescription = "补检号源启动日期")]
        public virtual DateTime? OpenDate { get; set; }

        /// <summary>
        /// 补检设置次数
        /// </summary>
        [SugarColumn(ColumnName = "SetCount", ColumnDescription = "补检设置次数")]
        public virtual int SetCount { get; set; } = 0;
    }
}
