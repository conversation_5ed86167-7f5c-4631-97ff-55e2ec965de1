﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Entities
{
    [SugarTable(TableName = "ItemTemplate", TableDescription = "套餐加项模版")]
    public class ItemTemplate: BaseEntity
    {

        [SugarColumn(IsNullable = false, ColumnName = "ItemTemplateTitle", ColumnDescription = "加项模版标题")]
        public string ItemTemplateTitle { get; set; }


        [SugarColumn(IsNullable = false, ColumnName = "ItemTemplateId", ColumnDescription = "加项模版编码")]
        public string ItemTemplateId { get; set; }
        


        [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "项目编码")]
        public string CombCode { get; set; }
    }
}
