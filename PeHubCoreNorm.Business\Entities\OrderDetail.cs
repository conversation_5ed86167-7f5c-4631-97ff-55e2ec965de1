﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单明细表
/// </summary>
[SugarTable("OrderDetail", TableDescription = "订单明细表")]
public class OrderDetail
{
    [SugarColumn(IsNullable = false, ColumnName = "OrderId", ColumnDescription = "订单Id")]
    public string OrderId { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombName", ColumnDescription = "组合名称", Length = 200)]
    public string CombName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Price", ColumnDescription = "价格", Length = 9, DecimalDigits = 2)]
    public decimal Price { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombType", ColumnDescription = "组合类型(1:套餐 2:加项包)")]
    public int CombType { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "OrderType", ColumnDescription = "订单类型(1:个人 2:团体)")]
    public int OrderType { get; set; }
}