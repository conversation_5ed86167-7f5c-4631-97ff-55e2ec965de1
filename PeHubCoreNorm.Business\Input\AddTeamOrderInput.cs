﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Input
{
    public class AddTeamOrderInput
    {

        /// <summary>
        /// 用户Id
        /// </summary>
        public  string MembersId {  get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 卡号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 卡类型
        /// </summary>
        public string CardType { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 人员分类代码
        /// </summary>
        public string PeClsCode { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        public string Birthday { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Sex { get; set; }

        /// <summary>
        /// 婚姻状况
        /// </summary>
        public string MarryStatus { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string CompanyCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司次数
        /// </summary>
        public int CompanyTimes { get; set; }

        /// <summary>
        /// 套餐代码
        /// </summary>
        public string ClusCode { get; set; }

        /// <summary>
        /// 套餐名称
        /// </summary>
        public string ClusName { get; set; }


        /// <summary>
        /// 总价
        /// </summary>
        public decimal TotalPrice { get; set; }

        /// <summary>
        /// 补检标识
        /// </summary>
        public bool IsResidual { get; set; }

        /// <summary>
        /// 来源类型ID
        /// </summary>
        public string SourceTypeID { get; set; }

        /// <summary>
        /// 时间段ID
        /// </summary>
        public string TimeSlotID { get; set; }

        /// <summary>
        /// 时间段名称
        /// </summary>
        public string TimeSlotEntryName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public string BeginTime { get; set; }


        /// <summary>
        /// 院区编码
        /// </summary>
        public string AreaCode {  get; set; }


        /// <summary>
        /// 开单医生院号
        /// </summary>
         public string OperatorCode {  get; set; }

        /// <summary>
        /// 开单医生姓名
        /// </summary>
        public string OperatorName {  get; set; }


    }
}
