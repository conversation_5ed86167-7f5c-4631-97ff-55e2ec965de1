﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 个检订单入参
/// </summary>
public class PersonOrderInput
{
    /// <summary>
    /// 用户id
    /// </summary>
    [Required(ErrorMessage = "用户id不能为空")]
    public string MembersId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [Required(ErrorMessage = "姓名不能为空")]
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    [Required(ErrorMessage = "证件号不能为空")]
    public string CardNo { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    [Required(ErrorMessage = "证件类型不能为空")]
    public string CardType { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    [Required(ErrorMessage = "手机号不能为空")]
    public string Tel { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateTime Birthday { get; set; }

    /// <summary>
    /// 性别(1:男 2:女)
    /// </summary>
    [Required(ErrorMessage = "性别不能为空")]
    public string Sex { get; set; }

    /// <summary>
    /// 婚姻状态
    /// </summary>
    public string? MarryStatus { get; set; }

    /// <summary>
    /// 体检分类代码
    /// </summary>
    [Required(ErrorMessage = "体检分类不能为空")]
    public string PeClsCode { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    [Required(ErrorMessage = "套餐编码不能为空")]
    public string ClusCode { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    [Required(ErrorMessage = "套餐名称不能为空")]
    public string ClusName { get; set; }

    /// <summary>
    /// 订单总价格
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 号源类型
    /// </summary>
    [Required(ErrorMessage = "号源类型不能为空")]
    public string SourceTypeID { get; set; }

    /// <summary>
    /// 时间段编码Id
    /// </summary>
    [Required(ErrorMessage = "时间段编码Id不能为空")]
    public string TimeSlotID { get; set; }

    /// <summary>
    /// 时间段名称
    /// </summary>
    [Required(ErrorMessage = "时间段名称不能为空")]
    public string TimeSlotEntryName { get; set; }

    /// <summary>
    /// 客户体检时间
    /// </summary>
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 院区编码
    /// </summary>
    [Required(ErrorMessage = "院区编码不能为空")]
    public string AreaCode { get; set; }

    /// <summary>
    /// 开单医生工号
    /// </summary>
    [Required(ErrorMessage = "开单医生工号不能为空")]
    public string OperatorCode { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    [Required(ErrorMessage = "开单医生名称不能为空")]
    public string OperatorName { get; set; }

    /// <summary>
    /// 订单详情
    /// </summary>
    public List<PersonOrderDetailInput> OrderDetail { get; set; }
}

/// <summary>
/// 订单明细入参
/// </summary>
public class PersonOrderDetailInput
{
    /// <summary>
    /// 组合编码
    /// </summary>
    [Required(ErrorMessage = "组合编码不能为空")]
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    [Required(ErrorMessage = "组合名称不能为空")]
    public string CombName { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    [Required(ErrorMessage = "价格不能为空")]
    public decimal Price { get; set; }

    /// <summary>
    /// 组合类型(1:套餐 2:加项包)
    /// </summary>
    [Required(ErrorMessage = "组合类型不能为空")]
    public int CombType { get; set; }

    /// <summary>
    /// 订单类型(1:个人 2:团体)
    /// </summary>
    [Required(ErrorMessage = "订单类型不能为空")]
    public int OrderType { get; set; }
}