﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 后台管理个检订单查询
/// </summary>
public class PersonOrderPageInput : BasePageInput
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 截止日期
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 体检分类编码
    /// </summary>
    public string PeClsCode { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public OrderStatus OrderStatus { get; set; }
}