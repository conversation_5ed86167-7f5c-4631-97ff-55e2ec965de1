﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 报告详情入参
/// </summary>
public class ReportDetailInput
{
    /// <summary>
    /// 姓名
    /// </summary>
    [Required(ErrorMessage = "姓名不能为空")]
    public string Name { get; set; }

    /// <summary>
    /// 体检号
    /// </summary>
    [Required(ErrorMessage = "体检号不能为空")]
    public string RegNo { get; set; }

    /// <summary>
    /// 职检标识
    /// </summary>
    [Required(ErrorMessage = "职检标识不能为空")]
    public bool IsOccupation { get; set; }
}
