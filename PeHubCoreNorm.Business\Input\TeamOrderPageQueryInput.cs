namespace PeHubCoreNorm.Business.Input;


public class Page {


    /// </summary>
    public virtual int pageNum { get; set; } = 1;

    /// <summary>
    ///     每页条数
    /// </summary>
    [Range(1, 2000, ErrorMessage = "页码容量超过最大限制")]
    public virtual int pageSize { get; set; } = 10;

}
/// <summary>
/// 团检订单分页查询输入
/// </summary>
public class TeamOrderPageQueryInput : Page
{
    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 单位体检次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public OrderStatus? Status { get; set; }

    /// <summary>
    /// 体检人姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string? CardNo { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

}
