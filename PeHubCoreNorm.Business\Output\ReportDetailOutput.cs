﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 报告详情返参数
/// </summary>
public class ReportDetailOutput
{
    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }

    /// <summary>
    /// 体检号
    /// </summary>
    public string RegNo { get; set; }

    /// <summary>
    /// 体检日期
    /// </summary>
    public string RegisterDate { get; set; }

    /// <summary>
    /// 审核时间
    /// </summary>
    public string AuditTime { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 主检医生
    /// </summary>
    public string CheckDoctorName { get; set; }

    /// <summary>
    /// 综述
    /// </summary>
    public string Summary { get; set; }

    /// <summary>
    /// 建议
    /// </summary>
    public string Suggestion { get; set; }

    /// <summary>
    /// 结论
    /// </summary>
    public string Conclusion { get; set; }

    /// <summary>
    /// 职检标识
    /// </summary>        
    public bool IsOccupation { get; set; }

    /// <summary>
    /// 体检结果记录
    /// </summary>
    public List<PeRecord> Itemlist { get; set; }
}

/// <summary>
/// 体检结果记录
/// </summary>
public class PeRecord
{
    /// <summary>
    /// 组合代码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 组合小结
    /// </summary>
    public string CombResult { get; set; }

    /// <summary>
    /// 检查类型
    /// </summary>
    public int CheckCls { get; set; }

    /// <summary>
    /// 医生名称
    /// </summary>
    public string DoctorName { get; set; }

    /// <summary>
    /// 项目结果明细
    /// </summary>
    public List<ItemDetail> DetailList { get; set; }
}

/// <summary>
/// 项目结果明细
/// </summary>
public class ItemDetail
{
    /// <summary>
    /// 项目代码
    /// </summary>
    public string ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ItemName { get; set; }

    /// <summary>
    /// 项目结果
    /// </summary>
    public string ItemResult { get; set; }

    /// <summary>
    /// 结果单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 结果下限
    /// </summary>
    public string LowerLimit { get; set; }

    /// <summary>
    /// 结果上限
    /// </summary>
    public string UpperLimit { get; set; }

    /// <summary>
    /// 提示符号
    /// </summary>
    public string Hint { get; set; }
}
