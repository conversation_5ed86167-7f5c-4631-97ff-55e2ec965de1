﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 报告列表返参
/// </summary>
public class ReportOutput
{
    /// <summary>
    /// 报告时间
    /// </summary>
    public string ReportDate { get; set; }

    /// <summary>
    /// 报告用户
    /// </summary>
    public string ReportName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string ReportSex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public string ReportAge { get; set; }

    /// <summary>
    /// 检查医生
    /// </summary>
    public string ReportDoctor { get; set; }

    /// <summary>
    /// 体检号
    /// </summary>
    public string RegNo { get; set; }

    /// <summary>
    /// 职检标识
    /// </summary>        
    public bool IsOccupation { get; set; }
}
