﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 套餐业务接口
/// </summary>
public class BasicCodeService : BizDbRepository<MapClusterAddPackage>, IBasicCodeService
{
    private readonly ILogger<BasicCodeService> _logger;
    public BasicCodeService(ILogger<BasicCodeService> logger)
    {
        _logger = logger;
    }

    #region 体检分类
    /// <summary>
    /// 获取体检分类
    /// </summary>
    /// <returns></returns>
    public async Task<PeCls[]> GetCodePeCls(bool isFilter = false)
    {
        return await Context.Queryable<CodePeCls>()
            .WhereIF(isFilter, x => x.IsEnabled == "Y")
            .Select<PeCls>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 添加体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    public async Task<bool> AddCodePeCls(PeCls peCls)
    {
        var data = Context.Queryable<CodePeCls>().First(x => x.ClsCode == peCls.ClsCode);
        if (data != null)
        { 
            Unify.SetError($"编码:{peCls.ClsCode}的体检分类已存在，请勿重复添加!");
            return false;
        }
		var itemCls = peCls.Adapt<CodePeCls>();
        return await Context.Insertable(itemCls).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 编辑体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    public async Task<bool> EditCodePeCls(PeCls peCls)
    {
        var data = Context.Queryable<CodePeCls>().First(x => x.ClsCode == peCls.ClsCode);
        if (data == null)
       {
            Unify.SetError($"编码:{peCls.ClsCode}的体检分类不存在，请刷新页面!");
            return false;
        }

        data.ClsName = peCls.ClsName;
        data.IsEnabled = peCls.IsEnabled;
        return await Context.Updateable(data).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 删除体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    public async Task<bool> DeleteCodePeCls(PeCls peCls)
    {
        return await Context.Deleteable<CodePeCls>().Where(x => x.ClsCode == peCls.ClsCode && x.ClsName == peCls.ClsName).ExecuteCommandAsync() > 0;
    }
    #endregion

    #region 加项包
    /// <summary>
    /// 获取加项包
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    public async Task<AddPackageOutput[]> GetAddPackage(AddPackageInput addInput)
    {
        return await Context.Queryable<AddPackage>()
               .WhereIF(!string.IsNullOrEmpty(addInput.SearchKey), x => x.AddPackageCode == addInput.SearchKey || x.AddPackageName.Contains(addInput.SearchKey))
               .WhereIF(!string.IsNullOrEmpty(addInput.Gender), x => x.Gender == addInput.Gender)
               .WhereIF(!string.IsNullOrEmpty(addInput.IsCompany), x => x.IsCompany == addInput.IsCompany)
               .WhereIF(!string.IsNullOrEmpty(addInput.CompanyCode), x => x.CompanyCode == addInput.CompanyCode)
               .Select(x => new AddPackageOutput
               {
                   Id = x.Id,
                   AddPackageCode = x.AddPackageCode,
                   AddPackageName = x.AddPackageName,
                   Gender = x.Gender,
                   Introduce = x.Introduce,
                   Price = x.Price,
                   IsCompany = x.IsCompany,
                   CompanyCode = x.CompanyCode,
                   CompanyName = SqlFunc.IIF(x.CompanyCode == "", "", SqlFunc.Subqueryable<Company>().Where(y => y.CompanyCode == x.CompanyCode).Select(y => y.CompanyName)),
                   OptionalQuantity = x.OptionalQuantity,
                   Status = x.Status,
                   CreateDate = x.CreateDate
               })
               .ToArrayAsync();
    }

    /// <summary>
    /// 保存加项包
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    public async Task<bool> SaveAddPackage(SaveAddPackageAndDetail addInput)
    {
        var data = Context.Queryable<AddPackage>().First(x => x.AddPackageCode == addInput.AddPackageCode);
        if (data == null)
        {
            addInput.AddPackageCode = IDUtils.NextBusinessNo("A");
            var addPkg = addInput.Adapt<AddPackage>();
            await Context.Insertable(addPkg).ExecuteCommandAsync();
        }
        else
        {
            var addPkg = addInput.Adapt<AddPackage>();
            await Context.Updateable(addPkg).ExecuteCommandAsync();
            await Context.Deleteable<AddPackageDetail>().Where(x => x.AddPackageCode == addInput.AddPackageCode).ExecuteCommandAsync();
        }

        if (addInput.DetailCombs.Count == 0)
            return true;

        var addPkgDetail = addInput.DetailCombs.Select(x => new AddPackageDetail
        {
            AddPackageCode = addInput.AddPackageCode,
            CombCode = x.CombCode
        }).ToArray();
        await Context.Insertable(addPkgDetail).ExecuteCommandAsync();

        return true;
    }

    /// <summary>
    /// 删除加项包
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    public async Task<bool> DeleteAddPackage(BaseIdsInput baseId)
    {
        if (baseId.Ids.Length > 0)
            await Context.Deleteable<AddPackage>().In(baseId.Ids).ExecuteCommandAsync();

        // 先找出加项包
        var addPkgs = Context.Queryable<AddPackage>().Where(x => baseId.Ids.Contains(x.Id)).ToList();

        var addPkgCodes = addPkgs.Select(x => x.AddPackageCode).ToArray();
        // 找出加项包对应的组合
        var addPkgDetail = Context.Queryable<AddPackageDetail>().Where(x => addPkgCodes.Contains(x.AddPackageCode)).ToList();

        await Context.Deleteable(addPkgDetail).ExecuteCommandAsync();
        return await Context.Deleteable(addPkgs).ExecuteCommandAsync() > 0;
    }
    #endregion

    #region 加项包明细
    /// <summary>
    /// 加项包明细查询
    /// </summary>
    /// <param name="addDetail"></param>
    /// <returns></returns>
    public async Task<AddPackageDetailOutput[]> GetAddPackageDetail(AddPackageDetailInput addDetail)
    {
        if (string.IsNullOrEmpty(addDetail.addPkgCode))
            return Unify.SetError("加项包编码不能为空");

        return await Context.Queryable<AddPackageDetail>()
            .LeftJoin<CodeItemComb>((pkg, comb) => pkg.CombCode == comb.CombCode)
            .Where(pkg => pkg.AddPackageCode == addDetail.addPkgCode)
            .Select<AddPackageDetailOutput>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 更新加项包明细
    /// </summary>
    /// <param name="addPackages"></param>
    /// <returns></returns>
    public async Task<bool> EditAddPackageDetail(List<AddPackageDetailEditInput> addPackages)
    {
        var addPackageCode = addPackages[0].AddPackageCode;
        await Context.Deleteable<AddPackageDetail>().Where(x => x.AddPackageCode == addPackageCode).ExecuteCommandAsync();

        var addPackagesData = addPackages.Adapt<AddPackageDetail>();
        return await Context.Insertable(addPackagesData).ExecuteCommandAsync() > 0;
    }
    #endregion

    #region 项目分类
    /// <summary>
    /// 获取项目分类
    /// </summary>
    /// <returns></returns>
    public async Task<CodeItemClsOutput[]> GetCodeItemCls()
    {
        return await Context.Queryable<CodeItemCls>()
            .Select<CodeItemClsOutput>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 添加项目分类
    /// </summary>
    /// <param name="itemClsInput"></param>
    /// <returns></returns>
    public async Task<bool> AddCodeItemCls(CodeItemClsAddInput itemClsInput)
    {
        var data = Context.Queryable<CodeItemCls>().First(x => x.ClsCode == itemClsInput.ClsCode);
        if (data != null)
        {
            Unify.SetError($"编码:{itemClsInput.ClsCode}的项目分类已存在，请勿重复添加!");
            return false;
		}

        var itemCls = itemClsInput.Adapt<CodeItemCls>();
        return await Context.Insertable(itemCls).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 编辑项目分类
    /// </summary>
    /// <param name="itemClsInput"></param>
    /// <returns></returns>
    public async Task<bool> EditCodeItemCls(CodeItemClsEditInput itemClsInput)
    {
        var data = Context.Queryable<CodeItemCls>().First(x => x.Id == itemClsInput.Id);
        if (data == null)
        {
            Unify.SetError($"编码:{itemClsInput.ClsCode}的项目分类不存在，请刷新页面!");
            return false;
        }

        var itemCls = itemClsInput.Adapt<CodeItemCls>();
        return await Context.Updateable(itemCls).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 删除项目分类
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    public async Task<bool> DeleteCodeItemCls(BaseIdsInput baseId)
    {
        //获取所有ID
        if (baseId.Ids.Length > 0)
            await Context.Deleteable<CodeItemCls>().In(baseId.Ids).ExecuteCommandAsync(); //删除数据

        return true;
    }
    #endregion

    #region 组合
    /// <summary>
    /// 获取组合
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<CodeItemCombOutput>> GetCodeItemComb(CodeItemCombPageInput input)
    {
        var query = Context.Queryable<CodeItemComb>()
            .LeftJoin<CodeItemCls>((item, itemcls) => item.ClsCode == itemcls.ClsCode)
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey), item => item.CombCode.Contains(input.SearchKey) || item.CombName.Contains(input.SearchKey))//根据关键字查询
            .Select<CodeItemCombOutput>();

        var pageInfo = await query.ToPagedListAsync(input.pageNum, input.pageSize); //分页
        return pageInfo;
    }

    /// <summary>
    /// 获取组合(不分页查询)
    /// </summary>
    /// <returns></returns>
    public async Task<CodeItemCombOutput[]> GetAllCodeItemComb()
    {
        return await Context.Queryable<CodeItemComb>()
           .LeftJoin<CodeItemCls>((item, itemcls) => item.ClsCode == itemcls.ClsCode)
           .Select<CodeItemCombOutput>()
           .ToArrayAsync();
    }

    /// <summary>
    /// 添加组合
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    public async Task<bool> AddCodeItemComb(CodeItemCombAddInput addInput)
    {
        var data = Context.Queryable<CodeItemComb>().First(x => x.CombCode == addInput.CombCode);
        if (data != null)
        {
            Unify.SetError($"编码:{addInput.CombCode}的组合已存在，请勿重复操作!");
            return false;
        }

        var itemComb = addInput.Adapt<CodeItemComb>();
        return await Context.Insertable(itemComb).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 编辑组合
    /// </summary>
    /// <param name="editInput"></param>
    /// <returns></returns>
    public async Task<bool> EditCodeItemComb(CodeItemCombEditInput editInput)
    {
        var data = Context.Queryable<CodeItemComb>().First(x => x.Id == editInput.Id);
        if (data == null)
        {
            Unify.SetError($"编码:{editInput.CombCode}的组合不存在，请刷新页面!");
            return false;
        }

        var itemComb = editInput.Adapt<CodeItemComb>();
        return await Context.Updateable(itemComb).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 删除组合
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    public async Task<bool> DeleteCodeItemComb(BaseIdsInput baseId)
    {
        if (baseId.Ids.Length > 0)
            await Context.Deleteable<CodeItemComb>().In(baseId.Ids).ExecuteCommandAsync();

        return true;
    }
    #endregion

    #region 个检套餐
    /// <summary>
    /// 获取个检套餐
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<CodeClusterOutput>> GetCodeCluster(CodeClusterPageInput clusterInput)
    {
        var query = Context.Queryable<CodeCluster>()
            .WhereIF(!string.IsNullOrEmpty(clusterInput.SearchKey), cluster => cluster.ClusCode.Contains(clusterInput.SearchKey) || cluster.ClusName.Contains(clusterInput.SearchKey))
            .WhereIF(!string.IsNullOrEmpty(clusterInput.PeCls), cluster => cluster.PeCls == clusterInput.PeCls)
            .WhereIF(!string.IsNullOrEmpty(clusterInput.IsFixed), cluster => cluster.IsFixed == clusterInput.IsFixed)
            .Select<CodeClusterOutput>();

        var pageInfo = await query.ToPagedListAsync(clusterInput.pageNum, clusterInput.pageSize); //分页
        return pageInfo;
    }

    /// <summary>
    /// 获取套餐对应的组合
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    public async Task<MapClusterCombOutput[]> GetMapClusterComb(MapClusterCombInput clusterInput)
    {
        return await Context.Queryable<MapClusterComb>()
            .InnerJoin<CodeCluster>((mapCluster, cluster) => mapCluster.ClusCode == cluster.ClusCode)
            .InnerJoin<CodeItemComb>((mapCluster, cluster, comb) => comb.CombCode == mapCluster.CombCode)
            .Where(mapCluster => mapCluster.ClusCode == clusterInput.ClusCode)
            .Select<MapClusterCombOutput>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 保存套餐信息及对应组合
    /// </summary>
    /// <param name="saveCluster"></param>
    /// <returns></returns>
    public async Task<bool> SaveClusterAndMapCombs(SaveClusterAndMapComb saveCluster)
    {
        var data = Context.Queryable<CodeCluster>().First(x => x.ClusCode == saveCluster.ClusCode);
        if (data == null)
        {
            saveCluster.ClusCode = IDUtils.NextBusinessNo("C");
            var cluster = saveCluster.Adapt<CodeCluster>();
            cluster.IsFixed = "N";// 新建的套餐都不是固定套餐
            await Context.Insertable(cluster).ExecuteCommandAsync();
        }
        else
        {
            var cluster = saveCluster.Adapt<CodeCluster>();
            await Context.Updateable(cluster).ExecuteCommandAsync();
            await Context.Deleteable<MapClusterComb>().Where(x => x.ClusCode == saveCluster.ClusCode).ExecuteCommandAsync();
        }

        if (saveCluster.ClusterCombs.Count == 0)
            return true;

        var mapCombs = saveCluster.ClusterCombs.Distinct().Adapt<List<MapClusterComb>>();
        return await Context.Insertable(mapCombs).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 删除个检套餐
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    public async Task<bool> DeleteCodeCluster(BaseIdsInput baseId)
    {
        // 先找出套餐
        var clusters = Context.Queryable<CodeCluster>().Where(x => baseId.Ids.Contains(x.Id)).ToList();

        var clusterCodes = clusters.Select(x => x.ClusCode).ToArray();
        // 找出套餐对应的组合,可加项组合，对应的加项包
        var mapClusterCombs = Context.Queryable<MapClusterComb>().Where(x => clusterCodes.Contains(x.ClusCode)).ToList();// 套餐对应的组合
        var mapClusterExtraComb = Context.Queryable<MapClusterExtraComb>().Where(x => clusterCodes.Contains(x.ClusCode)).ToList();// 套餐外的组合
        var mapClusterAddPackage = Context.Queryable<MapClusterAddPackage>().Where(x => clusterCodes.Contains(x.ClusCode)).ToList();// 套餐对应的加项包

        await Context.Deleteable(mapClusterAddPackage).ExecuteCommandAsync();
        await Context.Deleteable(mapClusterExtraComb).ExecuteCommandAsync();
        await Context.Deleteable(mapClusterCombs).ExecuteCommandAsync();
        return await Context.Deleteable(clusters).ExecuteCommandAsync() > 0;
    }
    #endregion

    #region 个检套餐外的项目
    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<MapClusterExtraCombOutput[]> GetMapClusterExtraComb(MapClusterExtraCombInput input)
    {
        return await Context.Queryable<MapClusterExtraComb>()
               .InnerJoin<CodeCluster>((mapCluster, cluster) => mapCluster.ClusCode == cluster.ClusCode)
               .InnerJoin<CodeItemComb>((mapCluster, cluster, comb) => comb.CombCode == mapCluster.CombCode)
               .Where(mapCluster => mapCluster.ClusCode == input.ClusCode)
               .Select<MapClusterExtraCombOutput>()
               .ToArrayAsync();
    }

    /// <summary>
    /// 保存个检套餐外的项目
    /// </summary>
    /// <param name="extraCombs"></param>
    /// <returns></returns>
    public async Task<bool> SaveMapClusterExtraComb(ClusterExtraComb[] extraCombs)
    {
        if (extraCombs.Length == 0)
        {   
            Unify.SetError("数据不能为空!");
            return false;
        }

		var clusCode = extraCombs.First().ClusCode;
        var combsArray = extraCombs.Select(x => x.CombCode).ToArray();

        // 先查出套餐内是否存在extraCombs组合
        var existMapCombs = Context.Queryable<MapClusterComb>()
            .InnerJoin<CodeItemComb>((cluster, comb) => cluster.CombCode == comb.CombCode)
            .Where(cluster => cluster.ClusCode == clusCode && SqlFunc.ContainsArray(combsArray, cluster.CombCode))
            .Select((cluster, comb) => comb.CombName)
            .ToArray();
        if (existMapCombs.Length != 0)
        {
            Unify.SetError($"套餐内已存在{string.Join("、", existMapCombs)}组合,请勿错误操作!");
			return false;
		}

        var mapExtraCombs = extraCombs.Distinct().Adapt<List<MapClusterExtraComb>>();
        await Context.Deleteable<MapClusterExtraComb>(x => x.ClusCode == clusCode).ExecuteCommandAsync();
        return await Context.Insertable(mapExtraCombs).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 保存单位套餐外的项目
    /// </summary>
    /// <param name="extraCombs"></param>
    /// <returns></returns>
    public async Task<bool> SaveMapCompanyClusterExtraComb(ClusterExtraComb[] extraCombs)
    {
        if (extraCombs.Length == 0)
        {
            Unify.SetError("数据不能为空!");
            return false;
        }
        var clusCode = extraCombs[0].ClusCode;
        var combsArray = extraCombs.Select(x => x.CombCode).ToArray();
        // 先查出套餐内是否存在extraCombs组合
        var existMapCombs = Context.Queryable<MapClusterComb>()
            .InnerJoin<CodeItemComb>((cluster, comb) => cluster.CombCode == comb.CombCode)
            .Where(cluster => cluster.ClusCode == clusCode && SqlFunc.ContainsArray(combsArray, cluster.CombCode))
            .Select((cluster, comb) => comb.CombName)
            .ToArray();
        if (existMapCombs.Length != 0)
        {
            Unify.SetError($"套餐内已存在{string.Join("、", existMapCombs)}组合,请勿错误操作!");
            return false;
        }

        var mapExtraCombs = extraCombs.Distinct().Adapt<List<MapCompanyClusterExtraComb>>();
        await Context.Deleteable<MapCompanyClusterExtraComb>(x => x.ClusCode == clusCode).ExecuteCommandAsync();
        return await Context.Insertable(mapExtraCombs).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 获取单位套餐外的项目
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<MapClusterExtraCombOutput[]> GetCompanyClusterExtraComb(MapClusterExtraCombInput input)
    {
        return await Context.Queryable<MapCompanyClusterExtraComb>()
               .InnerJoin<CompanyCluster>((mapCluster, cluster) => mapCluster.ClusCode == cluster.ClusCode)
               .InnerJoin<CodeItemComb>((mapCluster, cluster, comb) => comb.CombCode == mapCluster.CombCode)
               .Where(mapCluster => mapCluster.ClusCode == input.ClusCode)
               .Select<MapClusterExtraCombOutput>()
               .ToArrayAsync();
    }
    #endregion

    #region 套餐加项包
    ///<summary>
    /// 套餐加项包查询
    /// </summary>
    /// <param name="clusterAddPkgInput"></param>
    /// <returns></returns>
    /// <summary>
    public async Task<ClusterAddPackageOutput[]> GetClusterAddPackage(ClusterAddPackageInput clusterAddPkgInput)
    {
        return await Context.Queryable<MapClusterAddPackage>()
             .LeftJoin<AddPackage>((cluster, add) => cluster.AddPackageCode == add.AddPackageCode)
             .Where(cluster => cluster.ClusCode == clusterAddPkgInput.ClusCode)
             .Select<ClusterAddPackageOutput>()
             .ToArrayAsync();
    }

    /// <summary>
    /// 保存套餐加项包
    /// </summary>
    /// <param name="packages"></param>
    /// <returns></returns>
    public async Task<bool> SaveClusterAddPackage(List<ClusterAddPackage> packages)
    {
        if (packages.Count == 0)
        {
            Unify.SetError("保存的数据不能为空!");
            return false;
        }
		await Context.Deleteable<MapClusterAddPackage>(x => x.ClusCode == packages[0].ClusCode).ExecuteCommandAsync();
        var updateData = packages.Adapt<List<MapClusterAddPackage>>();
        return await Context.Insertable(updateData).ExecuteCommandAsync() > 0;
    }

    public async Task<ClusterAddPackageOutput[]> GetCompanyClusterAddPackage(ClusterAddPackageInput clusterAddPkgInput)
    {
         return await Context.Queryable<MapCompanyClusterAddPackage>()
             .LeftJoin<AddPackage>((cluster, add) => cluster.AddPackageCode == add.AddPackageCode)
             .Where(cluster => cluster.ClusCode == clusterAddPkgInput.ClusCode)
             .Select<ClusterAddPackageOutput>()
             .ToArrayAsync();
    }

    public async Task<bool> SaveCompanyClusterAddPackage(List<ClusterAddPackage> packages)
    {
        if (packages.Count == 0)
        {
            Unify.SetError("保存的数据不能为空!");
            return false;
        }
        await Context.Deleteable<MapCompanyClusterAddPackage>(x => x.ClusCode == packages[0].ClusCode).ExecuteCommandAsync();
        var updateData = packages.Adapt<List<MapCompanyClusterAddPackage>>();
        return await Context.Insertable(updateData).ExecuteCommandAsync() > 0;

    }
    #endregion

        #region 互斥组合关系
        /// <summary>
        /// 互斥组合关系查询
        /// </summary>
        /// <returns></returns>
    public async Task<MutexCombOutput[]> GetMutexCombs()
    {
        var mutexCombs = await Context.Queryable<CodeMutexComb>().ToArrayAsync();
        return mutexCombs.GroupBy(x => x.MutexCode)
            .Select(x => new MutexCombOutput
            {
                MutexCode = x.First().MutexCode,
                MutexName = x.First().MutexName,
                MutexCombs = string.Join("、", x.Select(sub => sub.CombName))
            }).ToArray();
    }

    /// <summary>
    /// 删除互斥关系组合
    /// </summary>
    /// <param name="mutexCode"></param>
    /// <returns></returns>
    public async Task<bool> DeleteMutexComb(string[] mutexCode)
    {
        return await Context.Deleteable<CodeMutexComb>()
            .Where(x => SqlFunc.ContainsArray(mutexCode, x.MutexCode))
            .ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 获取互斥组合明细
    /// </summary>
    /// <param name="mutexCode"></param>
    /// <returns></returns>
    public async Task<MutexCombDetailOutput[]> GetMutexCombDetail(string mutexCode)
    {
        return await Context.Queryable<CodeMutexComb>()
            .Where(x => x.MutexCode == mutexCode)
            .Select<MutexCombDetailOutput>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 保存互斥组合明细
    /// </summary>
    /// <param name="mutexCombs"></param>
    /// <returns></returns>
    public async Task<bool> SaveMutexCombDetail(List<MutexCombDetailInput> mutexCombs)
    {
        if (mutexCombs.Count == 0)
        {
			Unify.SetError("数据不能为空!");
            return false;

		}

        var mutexCode = mutexCombs[0].MutexCode;
        if (string.IsNullOrEmpty(mutexCode))
        {
            mutexCode = IDUtils.NextBusinessNo("M");
            mutexCombs.ForEach(x => x.MutexCode = mutexCode);
        }
        else
        {
            // 查询数据库有无数据
            var mutexArray = Context.Queryable<CodeMutexComb>().Where(x => x.MutexCode == mutexCode).ToArray();
            if (mutexArray.Length != 0)
                await Context.Deleteable<CodeMutexComb>(x => x.MutexCode == mutexCode).ExecuteCommandAsync();
        }

        // 实体转换
        var mutexComb = mutexCombs.Adapt<List<CodeMutexComb>>();
        return await Context.Insertable(mutexComb).ExecuteCommandAsync() > 0;
    }
    #endregion

    #region 组合包含关系
    /// <summary>
    /// 组合包含关系查询
    /// </summary>
    /// <returns></returns>
    public async Task<MapCombContainOutput[]> GetCombContain()
    {
        var combContains = await Context.Queryable<MapCombContain>().ToArrayAsync();
        return combContains.GroupBy(x => x.CombCode)
            .Select(x => new MapCombContainOutput
            {
                CombCode = x.First().CombCode,
                CombName = x.First().CombName,
                ChildCombs = string.Join("、", x.Select(sub => sub.ChildCombName))
            }).ToArray();
    }

    /// <summary>
    /// 删除组合包含关系
    /// </summary>
    /// <param name="combCode"></param>
    /// <returns></returns>
    public async Task<bool> DeleteCombContain(string[] combCode)
    {
        return await Context.Deleteable<MapCombContain>()
           .Where(x => SqlFunc.ContainsArray(combCode, x.CombCode))
           .ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 获取组合包含关系明细
    /// </summary>
    /// <param name="combCode"></param>
    /// <returns></returns>
    public async Task<CombContainDetailOutput[]> GetCombContainDetail(string combCode)
    {
        return await Context.Queryable<MapCombContain>()
          .Where(x => x.CombCode == combCode)
          .Select<CombContainDetailOutput>()
          .ToArrayAsync();
    }

    /// <summary>
    /// 保存组合包含关系
    /// </summary>
    /// <param name="containDetail"></param>
    /// <returns></returns>
    public async Task<bool> SaveCombContainDetail(List<CombContainDetailInput> containDetail)
    {
        if (containDetail.Count == 0)
        {
            Unify.SetError("数据不能为空!");
            return false;
		}
        var combCode = containDetail[0].CombCode;
        // 查询数据库有无数据
        var combCodeArray = Context.Queryable<MapCombContain>().Where(x => x.CombCode == combCode).ToArray();
        // 实体转换
        var combContain = containDetail.Adapt<List<MapCombContain>>();

        if (combCodeArray.Length != 0)
            await Context.Deleteable<MapCombContain>(x => x.CombCode == combCode).ExecuteCommandAsync();

        return await Context.Insertable(combContain).ExecuteCommandAsync() > 0;
    }
    #endregion

    #region 组合依赖关系
    /// <summary>
    /// 组合依赖关系查询
    /// </summary>
    /// <returns></returns>
    public async Task<MapCombDependenceOutput[]> GetMapCombDependence()
    {
        var combDependence = await Context.Queryable<MapCombDependence>().ToArrayAsync();
        return combDependence.GroupBy(x => x.CombCode)
            .Select(x => new MapCombDependenceOutput
            {
                CombCode = x.First().CombCode,
                CombName = x.First().CombName,
                DependOnCombs = string.Join("、", x.Select(sub => sub.DependOnCombName))
            }).ToArray();
    }

    /// <summary>
    /// 删除组合依赖关系
    /// </summary>
    /// <param name="combCodes"></param>
    /// <returns></returns>
    public async Task<bool> DeleteCombDependence(string[] combCodes)
    {
        return await Context.Deleteable<MapCombDependence>()
              .Where(x => SqlFunc.ContainsArray(combCodes, x.CombCode))
              .ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 获取组合依赖关系明细
    /// </summary>
    /// <param name="combCode"></param>
    /// <returns></returns>
    public async Task<CombDependenceDetail[]> GetCombDependenceDetail(string combCode)
    {
        return await Context.Queryable<MapCombDependence>()
               .Where(x => x.CombCode == combCode)
               .Select<CombDependenceDetail>()
               .ToArrayAsync();
    }

    /// <summary>
    /// 保存组合依赖关系明细
    /// </summary>
    /// <param name="dependDetail"></param>
    /// <returns></returns>
    public async Task<bool> SaveCombDependenceDetail(List<CombDependenceDetailInput> dependDetail)
    {
        if (dependDetail.Count == 0)
        {
            Unify.SetError("数据不能为空!");
            return false;
        }

        var combCode = dependDetail[0].CombCode;
        // 查询数据库有无数据
        var combCodeArray = Context.Queryable<MapCombDependence>().Where(x => x.CombCode == combCode).ToArray();
        // 实体转换
        var combdepend = dependDetail.Adapt<List<MapCombDependence>>();

        if (combCodeArray.Length != 0)
            await Context.Deleteable<MapCombDependence>(x => x.CombCode == combCode).ExecuteCommandAsync();

        return await Context.Insertable(combdepend).ExecuteCommandAsync() > 0;
    }
    #endregion

    /// <summary>
    /// 获取按项目分类分组的组合
    /// </summary>
    /// <returns></returns>
    public async Task<ItemClsCombOutput[]> GetCombsByItemClsGroup()
    {
        var query = await Context.Queryable<CodeItemComb>()
             .LeftJoin<CodeItemCls>((comb, cls) => comb.ClsCode == cls.ClsCode)
             .Select((comb, cls) => new
             {
                 cls.ClsCode,
                 cls.ClsName,
                 comb.CombCode,
                 comb.CombName,
                 comb.Gender,
                 comb.Price,
                 comb.Description
             })
             .ToArrayAsync();

        return query.GroupBy(x => new { x.ClsCode, x.ClsName })
            .Select(x => new ItemClsCombOutput
            {
                ClsCode = x.Key.ClsCode,
                ClsName = x.Key.ClsName,
                CombData = x.Select(y => new CombData
                {
                    CombCode = y.CombCode,
                    CombName = y.CombName,
                    Gender = y.Gender,
                    Price = y.Price,
                    Description = y.Description
                }).ToArray()
            })
            .ToArray(); 
    }

   
}