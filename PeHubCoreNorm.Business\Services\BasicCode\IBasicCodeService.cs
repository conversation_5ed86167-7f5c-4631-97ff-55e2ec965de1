﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 套餐业务接口
/// </summary>
public interface IBasicCodeService : ITransient, ITenantDBTransient
{
    #region 体检分类
    /// <summary>
    /// 获取体检分类
    /// </summary>
    /// <returns></returns>
    Task<PeCls[]> GetCodePeCls(bool isFilter = false);

    /// <summary>
    /// 添加体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    Task<bool> AddCodePeCls(PeCls peCls);

    /// <summary>
    /// 编辑体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    Task<bool> EditCodePeCls(PeCls peCls);

    /// <summary>
    /// 删除体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    Task<bool> DeleteCodePeCls(PeCls peCls);
    #endregion

    #region 加项包
    /// <summary>
    /// 获取加项包
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    Task<AddPackageOutput[]> GetAddPackage(AddPackageInput addInput);

    /// <summary>
    /// 保存加项包
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    Task<bool> SaveAddPackage(SaveAddPackageAndDetail addInput);

    /// <summary>
    /// 删除加项包
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    Task<bool> DeleteAddPackage(BaseIdsInput baseId);
    #endregion

    #region 加项包明细
    /// <summary>
    /// 加项包明细查询
    /// </summary>
    /// <param name="addDetail"></param>
    /// <returns></returns>
    Task<AddPackageDetailOutput[]> GetAddPackageDetail(AddPackageDetailInput addDetail);

    /// <summary>
    /// 更新加项包明细
    /// </summary>
    /// <param name="addPackages"></param>
    /// <returns></returns>
    Task<bool> EditAddPackageDetail(List<AddPackageDetailEditInput> addPackages);
    #endregion

    #region 项目分类
    /// <summary>
    /// 获取项目分类
    /// </summary>
    /// <returns></returns>
    Task<CodeItemClsOutput[]> GetCodeItemCls();

    /// <summary>
    /// 添加项目分类
    /// </summary>
    /// <param name="itemClsInput"></param>
    /// <returns></returns>
    Task<bool> AddCodeItemCls(CodeItemClsAddInput itemClsInput);

    /// <summary>
    /// 编辑项目分类
    /// </summary>
    /// <param name="itemClsInput"></param>
    /// <returns></returns>
    Task<bool> EditCodeItemCls(CodeItemClsEditInput itemClsInput);

    /// <summary>
    /// 删除项目分类
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    Task<bool> DeleteCodeItemCls(BaseIdsInput baseId);
    #endregion

    #region 组合
    /// <summary>
    /// 获取组合(分页查询)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<CodeItemCombOutput>> GetCodeItemComb(CodeItemCombPageInput input);

    /// <summary>
    /// 获取组合(不分页查询)
    /// </summary>
    /// <returns></returns>
    Task<CodeItemCombOutput[]> GetAllCodeItemComb();

    /// <summary>
    /// 添加组合
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    Task<bool> AddCodeItemComb(CodeItemCombAddInput addInput);

    /// <summary>
    /// 编辑组合
    /// </summary>
    /// <param name="editInput"></param>
    /// <returns></returns>
    Task<bool> EditCodeItemComb(CodeItemCombEditInput editInput);

    /// <summary>
    /// 删除组合
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    Task<bool> DeleteCodeItemComb(BaseIdsInput baseId);

    #endregion

    #region 个检套餐
    /// <summary>
    /// 获取个检套餐
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<CodeClusterOutput>> GetCodeCluster(CodeClusterPageInput clusterInput);

    /// <summary>
    /// 获取个检套餐对应的组合
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    Task<MapClusterCombOutput[]> GetMapClusterComb(MapClusterCombInput clusterInput);

    /// <summary>
    /// 保存个检套餐信息及对应组合
    /// </summary>
    /// <param name="saveCluster"></param>
    /// <returns></returns>
    Task<bool> SaveClusterAndMapCombs(SaveClusterAndMapComb saveCluster);

    /// <summary>
    /// 删除个检套餐
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    Task<bool> DeleteCodeCluster(BaseIdsInput baseId);
    #endregion

    #region 个检套餐外的项目
    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<MapClusterExtraCombOutput[]> GetMapClusterExtraComb(MapClusterExtraCombInput input);

    /// <summary>
    /// 保存个检套餐外的项目
    /// </summary>
    /// <param name="extraCombs"></param>
    /// <returns></returns>
    Task<bool> SaveMapClusterExtraComb(ClusterExtraComb[] extraCombs);
    #endregion


    #region 单位套餐外的项目

    /// <summary>
    /// 保存单位套餐外的项目
    /// </summary>
    /// <param name="extraCombs"></param>
    /// <returns></returns>
    Task<bool> SaveMapCompanyClusterExtraComb(ClusterExtraComb[] extraCombs);

    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<MapClusterExtraCombOutput[]> GetCompanyClusterExtraComb(MapClusterExtraCombInput input);

    
    #endregion


    #region 套餐加项包
    /// <summary>
    /// 套餐加项包查询
    /// </summary>
    /// <param name="clusterAddPkgInput"></param>
    /// <returns></returns>
    Task<ClusterAddPackageOutput[]> GetClusterAddPackage(ClusterAddPackageInput clusterAddPkgInput);

    Task<ClusterAddPackageOutput[]> GetCompanyClusterAddPackage(ClusterAddPackageInput clusterAddPkgInput);

    /// <summary>
    /// 保存套餐加项包
    /// </summary>
    /// <param name="packages"></param>
    /// <returns></returns>
    Task<bool> SaveClusterAddPackage(List<ClusterAddPackage> packages);

    Task<bool> SaveCompanyClusterAddPackage(List<ClusterAddPackage> packages);



    #endregion

    #region 互斥组合关系
    /// <summary>
    /// 互斥组合关系查询
    /// </summary>
    /// <returns></returns>
    Task<MutexCombOutput[]> GetMutexCombs();

    /// <summary>
    /// 删除互斥组合关系
    /// </summary>
    /// <param name="mutexCode"></param>
    /// <returns></returns>
    Task<bool> DeleteMutexComb(string[] mutexCode);

    /// <summary>
    /// 获取互斥组合明细
    /// </summary>
    /// <param name="mutexCode"></param>
    /// <returns></returns>
    Task<MutexCombDetailOutput[]> GetMutexCombDetail(string mutexCode);

    /// <summary>
    /// 保存互斥组合明细
    /// </summary>
    /// <param name="mutexCombs"></param>
    /// <returns></returns>
    Task<bool> SaveMutexCombDetail(List<MutexCombDetailInput> mutexCombs);

    #endregion

    #region 组合包含关系
    /// <summary>
    /// 组合包含关系查询
    /// </summary>
    /// <returns></returns>
    Task<MapCombContainOutput[]> GetCombContain();

    /// <summary>
    /// 删除组合包含关系
    /// </summary>
    /// <param name="combCode"></param>
    /// <returns></returns>
    Task<bool> DeleteCombContain(string[] combCode);

    /// <summary>
    /// 获取组合包含关系明细
    /// </summary>
    /// <param name="combCode"></param>
    /// <returns></returns>
    Task<CombContainDetailOutput[]> GetCombContainDetail(string combCode);

    /// <summary>
    /// 保存组合包含关系
    /// </summary>
    /// <param name="containDetail"></param>
    /// <returns></returns>
    Task<bool> SaveCombContainDetail(List<CombContainDetailInput> containDetail);
    #endregion

    #region 组合依赖关系
    /// <summary>
    /// 组合依赖关系查询
    /// </summary>
    /// <returns></returns>
    Task<MapCombDependenceOutput[]> GetMapCombDependence();

    /// <summary>
    /// 删除组合依赖关系
    /// </summary>
    /// <param name="combCodes"></param>
    /// <returns></returns>
    Task<bool> DeleteCombDependence(string[] combCodes);

    /// <summary>
    /// 获取组合依赖关系明细
    /// </summary>
    /// <param name="combCode"></param>
    /// <returns></returns>
    Task<CombDependenceDetail[]> GetCombDependenceDetail(string combCode);

    /// <summary>
    /// 保存组合依赖关系明细
    /// </summary>
    /// <param name="dependDetail"></param>
    /// <returns></returns>
    Task<bool> SaveCombDependenceDetail(List<CombDependenceDetailInput> dependDetail);
    #endregion

    /// <summary>
    /// 获取按项目分类分组的组合
    /// </summary>
    /// <returns></returns>
    Task<ItemClsCombOutput[]> GetCombsByItemClsGroup();
}
