﻿using PeHubCoreNorm.Business.Entities;
using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Services.ItemTemplates
{
    public interface IItemTemplateService: ITransient, ITenantDBTransient
    {

       Task<int> CreateItemTemplates(List<ItemTemplate> input);


       Task<List<ItemTemplate>> GetItemTemplates();


    }
}
