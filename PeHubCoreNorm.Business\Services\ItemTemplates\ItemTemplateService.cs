﻿using Masuit.Tools.Strings;
using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using PeHubCoreNorm.Business.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Services.ItemTemplates
{
    public class ItemTemplateService : BizDbRepository<UnitPersonnelList>, IItemTemplateService
    {
        private readonly ILogger<ItemTemplateService> _logger;

        public ItemTemplateService(ILogger<ItemTemplateService> logger)
        {
            _logger = logger;
        }


        /// <summary>
        /// 创建加项模版
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<int> CreateItemTemplates(List<ItemTemplate> input)
        {
            return await Context.Insertable(input).ExecuteCommandAsync();
        }

        public Task<List<ItemTemplate>> GetItemTemplates()
        {
           return Context.Queryable<ItemTemplate>().ToListAsync();
        }


        /// <summary>
        /// 更新加项模版
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<int> UpdateItemTemplates(List<CreateItemTemplateInput> input)
        {

            return await Context.Updateable(input).WhereColumns(it => new { it.CombCode }).ExecuteCommandAsync();
        }


     


    }
}
