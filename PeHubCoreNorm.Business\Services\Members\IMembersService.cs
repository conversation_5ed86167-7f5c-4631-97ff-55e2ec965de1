﻿

namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位业务接口
/// </summary>
public interface IMembersService : ITransient, ITenantDBTransient
{
    /// <summary>
    /// 根据wId获取用户选项
    /// </summary>
    /// <param name="wId"></param>
    /// <returns></returns>
    Task<AuthMember> GetMembersByWId(string wId,string orgkey);

    /// <summary>
    /// 获取用户信息(根据name、wId、tel、cardNo获取）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<Members>> GetMembersList(MembersInput input);
    /// <summary>
    /// 根据id更新名称
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> UpdateMembersName(MembersNameInput input);

   
    Task<dynamic> GetCaptchaInfo();

    Task ValidValidCode(string validCode, string validCodeReqNo, bool isDelete = true);

    Task<AuthMember> authlogin(AuthFormInput loginInput);
    Task<AuthMember> authloginOther(AuthFormInput loginInput);



	#region 绑卡
	/// <summary>
	/// 插入卡，如果卡存在则更新卡的Tel、BirthDate、Sex、Marital、PatientId，返回Entity
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	Task<CardList> EditCardsReturnEntity(CardList input);

    /// <summary>
    /// 绑定卡，如果已存在则返回卡
    /// </summary>
    /// <param name="membersId"></param>
    /// <param name="cardId"></param>
    /// <param name="is_default">是否设为默认卡</param>
    /// <returns></returns>
    MapMembersIdCardId MembersBingsCards(string membersId, string cardId, bool is_default = false);
    /// <summary>
    /// 获取用户id绑定的卡(isDefault：是否默认卡true|false）
    /// </summary>
    /// <param name="membersId"></param>
    /// <returns></returns>
    List<DefaultAndCards> GetCardsById(string membersId);
    /// <summary>
    /// 用户id解绑卡
    /// </summary>
    /// <param name="membersId"></param>
    /// <param name="cardId"></param>
    /// <returns></returns>
    Task<bool> UnbindTheCard(string membersId, string cardId);
    /// <summary>
    /// 根据卡id获取卡信息
    /// </summary>
    /// <param name="cardId"></param>
    /// <returns></returns>
    Task<CardList> GetCardById(string cardId);
    /// <summary>
    /// 根据卡id更新卡的Name、Tel、BirthDate、Sex、Marital、PatientId、CardType、CardNo
    /// </summary>
    /// <param name="card"></param>
    /// <returns></returns>
    bool UpdateCard(CardList card);

    /// <summary>
    /// 获取卡列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<CardList>> GetCardList(CardListInput input);
    /// <summary>
    /// 删除健康卡
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    bool DeleteCardList(string[] ids);


    #endregion
}