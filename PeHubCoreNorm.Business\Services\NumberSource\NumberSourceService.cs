﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 号源管理业务接口
/// </summary>
public class NumberSourceService : BizDbRepository<NS_GlobalSource>, INumberSourceService
{
    private readonly ILogger<NumberSourceService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger"></param>
    public NumberSourceService(
        ILogger<NumberSourceService> logger)
    {
        _logger = logger;
    }

    #region 时段管理    

    /// <summary>
    /// 获取所有时段
    /// </summary>
    /// <returns></returns>
    public async Task<TimeSlotEntryOutput[]> GetTimeSlotEntry()
    {
        var query = Context.Queryable<NS_TimeSlotEntry>()
            .Select<TimeSlotEntryOutput>()
            .ToArrayAsync();
        return await query;
    }

    /// <summary>
    /// 添加时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> AddTimeSlotEntry(AddTimeSlotEntryInput input)
    {
        var data = Context.Queryable<NS_TimeSlotEntry>().First(x => x.Id == input.Id);
        if (data != null)
        {
            Unify.SetError($"编码:{input.Id}的时段已存在，请勿重复操作!");
            return false;
        }
        var timeSlotEntry = input.Adapt<NS_TimeSlotEntry>();
        return await Context.Insertable(timeSlotEntry).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 更新时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> EditTimeSlotEntry(EditTimeSlotEntryInput input)
    {
        var data = Context.Queryable<NS_TimeSlotEntry>().First(x => x.Id == input.Id);
        if (data == null)
        {
            Unify.SetError($"编码:{input.Id}的时段不存在，请刷新页面!");
            return false;
        }
        var timeSlotEntry = input.Adapt<NS_TimeSlotEntry>();
        return await Context.Updateable(timeSlotEntry).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 删除时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> DeleteTimeSlotEntry(DeleteTimeSlotEntryInput input)
    {
        if (input.Ids.Length > 0)
        {
            return await Context.Deleteable<NS_TimeSlotEntry>().In(input.Ids).ExecuteCommandAsync() > 0;
        }
        return false;
    }

    /// <summary>
    /// 获取指定类型的号源时段
    /// </summary>
    /// <returns></returns>
    public async Task<TimeSlotOutput[]> GetTimeSlot()
    {
        var query = Context.Queryable<NS_TimeSlot>()
            .InnerJoin<NS_TimeSlotEntry>((a, b) => a.TimeSlotEntryID == b.Id)
            .InnerJoin<NS_SourceTypes>((a, b) => a.SourceTypeID == b.Id)
            .Select<TimeSlotOutput>()
            .ToArrayAsync();
        return await query;
    }

    #endregion

    #region 号源类型关联时段

    /// <summary>
    /// 获取全部号源类型
    /// </summary>
    /// <returns></returns>
    public async Task<SourceTypeOutput[]> GetSourceType()
    {
        var query = Context.Queryable<NS_SourceTypes>()
            .Select<SourceTypeOutput>()
            .ToArrayAsync();
        return await query;
    }

    /// <summary>
    /// 通过号源类型获取已关联时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<TimeSlotOutput[]> GetTimeSlotByType(TimeSlotInput input)
    {
        var query = Context.Queryable<NS_TimeSlot>()
            .InnerJoin<NS_TimeSlotEntry>((ts, tse) => ts.TimeSlotEntryID == tse.Id)
            .InnerJoin<NS_SourceTypes>((ts, tse, st) => ts.SourceTypeID == st.Id)
            .Where(ts => ts.SourceTypeID == input.SourceTypeID).Select<TimeSlotOutput>().ToArrayAsync();

        return await query;
    }

    /// <summary>
    /// 添加号源类型对应时段编码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> AddTimeSlotByType(AddTimeSlotByTypeInput input)
    {
        NS_TimeSlot slot = Context.Queryable<NS_TimeSlot>().First(x => x.Id == input.SourceTypeID && x.TimeSlotEntryID == input.TimeSlotEntryID);
        if (slot == null)
        {
            return Unify.SetError($"号源类型：{input.SourceTypeID}对应,编码为:{input.TimeSlotEntryID}的关联关系已存在，请勿重复操作!");
        }
        var timeSlot = input.Adapt<NS_TimeSlot>();
        return await Context.Insertable(timeSlot).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 更新号源类型对应时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> EditTimeSlotByType(EditTimeSlotByTypeInput input)
    {
        List<NS_TimeSlot> slots = Context.Queryable<NS_TimeSlot>().Where(x => x.SourceTypeID == input.SourceTypeID).ToList();
        List<NS_TimeSlot> notInSlots = slots.Where(x => !input.TimeSlotEntryID.Contains(x.TimeSlotEntryID)).ToList();

        string[] timeSlotEntryList = slots.Select(x => x.TimeSlotEntryID).ToArray();
        // 找出array1中有但array2中没有的元素
        string[] onlyInArray1 = input.TimeSlotEntryID.Except(timeSlotEntryList).ToArray();
        if (notInSlots.Count > 0)
        {
            await Context.Deleteable<NS_TimeSlot>().In(notInSlots.Select(x => x.Id)).ExecuteCommandAsync();
        }
        foreach (var item in onlyInArray1)
        {
            NS_TimeSlot ts = new NS_TimeSlot();
            ts.SourceTypeID = input.SourceTypeID;
            ts.TimeSlotEntryID = item;
            await Context.Insertable(ts).ExecuteCommandAsync();
        }
        return true;
    }

    /// <summary>
    /// 删除号源类型对应时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> DeleteTimeSlotByType(DeleteTimeSlotByTypeInput input)
    {
        return await Context.Deleteable<NS_TimeSlotEntry>().In(input.Id).ExecuteCommandAsync() > 0;
    }

    #endregion

    #region 号源管理
    /// <summary>
    /// 获取个检类型
    /// </summary>
    /// <returns></returns>
    public async Task<PersonNumberSourceOutput[]> GetPersonType()
    {
        return await Context.Queryable<NS_SourceTypes>()
            .Where(x => !string.IsNullOrEmpty(x.ClsCode))
            .Select<PersonNumberSourceOutput>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 获取某月号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetNumberSource(NumberSourceInput input)
    {
        NumberSourceOutput[] output = Array.Empty<NumberSourceOutput>();
        List<NS_SourceTypes> sourceTypes = Context.Queryable<NS_SourceTypes>().ToList();
        string tableName = sourceTypes.First(x => x.Id == input.SourceTypeID).SourceTypeTable;
        switch (tableName)
        {
            case "NS_GlobalSource":
                output = await GetGlobalNumberSource(input);
                break;
            case "NS_IndividualSource":
                output = await GetPersonNumberSource(input);
                break;
            case "NS_GroupSource":
                output = await GetGroupNumberSource(input);
                break;
            case "NS_KeyProjectSource":
                output = await GetKeyProjectNumberSource(input);
                break;
            case "NS_ResidualSource":
                output = await GetResidualNumberSource(input);
                break;
        }
        return output;
    }

    /// <summary>
    /// 获取某月号源（全局）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetGlobalNumberSource(NumberSourceInput input)
    {
        return await Context.Queryable<NS_GlobalSource>()
            .Where(x => x.Date.ToString("yyyy-MM") == (input.Year + "-" + input.Month))
            .GroupBy(x => new { x.Date, x.IsVacation, x.Week })
            .Select(x => new NumberSourceOutput
            {
                Date = x.Date,
                TotalCapacity = SqlFunc.AggregateSumNoNull(x.TotalCapacity),
                UsedCapacity = SqlFunc.AggregateSumNoNull(x.UsedCapacity),
                AvailableCapacity = SqlFunc.AggregateSumNoNull(x.AvailableCapacity),
                Week = x.Week,
                IsVacation = x.IsVacation
            })
            .ToArrayAsync();
    }
    /// <summary>
    /// 获取某月号源（补检）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetResidualNumberSource(NumberSourceInput input)
    {
        return await Context.Queryable<NS_ResidualSource>()
            .Where(x => x.Date.ToString("yyyy-MM") == (input.Year + "-" + input.Month))
            .GroupBy(x => new { x.Date, x.IsVacation, x.Week })
            .Select(x => new NumberSourceOutput
            {
                Date = x.Date,
                TotalCapacity = SqlFunc.AggregateSumNoNull(x.TotalCapacity),
                UsedCapacity = SqlFunc.AggregateSumNoNull(x.UsedCapacity),
                AvailableCapacity = SqlFunc.AggregateSumNoNull(x.AvailableCapacity),
                Week = x.Week,
                IsVacation = x.IsVacation
            })
            .ToArrayAsync();
    }
    /// <summary>
    /// 获取某月号源（个检）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetPersonNumberSource(NumberSourceInput input)
    {
        return await Context.Queryable<NS_IndividualSource>()
            .Where(x => x.Date.ToString("yyyy-MM") == (input.Year + "-" + input.Month))
            .Where(x => x.SourceTypeID == input.SourceTypeID)
            .GroupBy(x => new { x.Date, x.IsVacation, x.Week })
            .Select(x => new NumberSourceOutput
            {
                Date = x.Date,
                TotalCapacity = SqlFunc.AggregateSumNoNull(x.TotalCapacity),
                UsedCapacity = SqlFunc.AggregateSumNoNull(x.UsedCapacity),
                AvailableCapacity = SqlFunc.AggregateSumNoNull(x.AvailableCapacity),
                Week = x.Week,
                IsVacation = x.IsVacation
            })
            .ToArrayAsync();
    }
    /// <summary>
    /// 获取某月号源(团体)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetGroupNumberSource(NumberSourceInput input)
    {
        return await Context.Queryable<NS_GroupSource>()
            .Where(x => x.Date.ToString("yyyy-MM") == (input.Year + "-" + input.Month))
            .Where(x => x.UnitCode == input.UnitCode)
            .GroupBy(x => new { x.Date, x.IsVacation, x.Week })
            .Select(x => new NumberSourceOutput
            {
                Date = x.Date,
                TotalCapacity = SqlFunc.AggregateSumNoNull(x.TotalCapacity),
                UsedCapacity = SqlFunc.AggregateSumNoNull(x.UsedCapacity),
                AvailableCapacity = SqlFunc.AggregateSumNoNull(x.AvailableCapacity),
                Week = x.Week,
                IsVacation = x.IsVacation
            })
            .ToArrayAsync();
    }
    /// <summary>
    /// 获取某月号源（重点号源）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetKeyProjectNumberSource(NumberSourceInput input)
    {
        return await Context.Queryable<NS_KeyProjectSource>()
            .Where(x => x.Date.ToString("yyyy-MM") == (input.Year + "-" + input.Month))
            .Where(x => x.CodeItemCode == input.UnitCode)
            .GroupBy(x => new { x.Date, x.IsVacation, x.Week })
            .Select(x => new NumberSourceOutput
            {
                Date = x.Date,
                TotalCapacity = SqlFunc.AggregateSumNoNull(x.TotalCapacity),
                UsedCapacity = SqlFunc.AggregateSumNoNull(x.UsedCapacity),
                AvailableCapacity = SqlFunc.AggregateSumNoNull(x.AvailableCapacity),
                Week = x.Week,
                IsVacation = x.IsVacation
            })
            .ToArrayAsync();
    }

    /// <summary>
    /// 获取某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetNumberSourceByDate(NumberSourceByDayInput input)
    {
        NumberSourceOutput[] output = Array.Empty<NumberSourceOutput>();
        List<NS_SourceTypes> sourceTypes = Context.Queryable<NS_SourceTypes>().ToList();
        string tableName = sourceTypes.First(x => x.Id == input.SourceTypeID).SourceTypeTable;
        switch (tableName)
        {
            case "NS_GlobalSource":
                output = await GetGlobalNumberSourceByDate(input);
                break;
            case "NS_IndividualSource":
                output = await GetPersonNumberSourceByDate(input);
                break;
            case "NS_GroupSource":
                output = await GetGroupNumberSourceByDate(input);
                break;
            case "NS_KeyProjectSource":
                output = await GetKeyProjectNumberSourceByDate(input);
                break;
            case "NS_ResidualSource":
                output = await GetResidualNumberSourceByDate(input);
                break;
        }
        return output;
    }

    /// <summary>
    /// 获取全局某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetGlobalNumberSourceByDate(NumberSourceByDayInput input)
    {
        return await Context.Queryable<NS_GlobalSource>()
                .InnerJoin<NS_TimeSlot>((source, time) => time.Id == source.TimeSlotID)
                .InnerJoin<NS_TimeSlotEntry>((source, time, timeEnrtry) => timeEnrtry.Id == time.TimeSlotEntryID)
                .Where(source => source.Date.ToString("yyyy-MM-dd") == (input.Year + "-" + input.Month + "-" + input.Day))
                .Select<NumberSourceOutput>()
                .ToArrayAsync();
    }

    /// <summary>
    /// 获取补检某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetResidualNumberSourceByDate(NumberSourceByDayInput input)
    {
        return await Context.Queryable<NS_ResidualSource>()
                .InnerJoin<NS_TimeSlot>((source, time) => time.Id == source.TimeSlotID)
                .InnerJoin<NS_TimeSlotEntry>((source, time, timeEnrtry) => timeEnrtry.Id == time.TimeSlotEntryID)
                .Where(source => source.Date.ToString("yyyy-MM-dd") == (input.Year + "-" + input.Month + "-" + input.Day))
                .Select((source, time, timeEnrtry) => new NumberSourceOutput
                {
                    TotalCapacity = source.TotalCapacity,
                    UsedCapacity = source.UsedCapacity,
                    AvailableCapacity = source.AvailableCapacity,
                    Date = source.Date,
                    Week = source.Week,
                    IsVacation = source.IsVacation,
                    TimeSlotID = source.TimeSlotID,
                    TimeSlotName = timeEnrtry.TimeSlotName,
                    IsResidual = true
                })
                .ToArrayAsync();
    }

    /// <summary>
    /// 获取个检某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetPersonNumberSourceByDate(NumberSourceByDayInput input)
    {
        return await Context.Queryable<NS_IndividualSource>()
                .InnerJoin<NS_TimeSlot>((source, time) => time.Id == source.TimeSlotID)
                .InnerJoin<NS_TimeSlotEntry>((source, time, timeEnrtry) => timeEnrtry.Id == time.TimeSlotEntryID)
                .Where(source => source.SourceTypeID == input.SourceTypeID)
                .Where(source => source.Date.ToString("yyyy-MM-dd") == (input.Year + "-" + input.Month + "-" + input.Day))
                .Select<NumberSourceOutput>()
                .ToArrayAsync();
    }

    /// <summary>
    /// 获取团检某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetGroupNumberSourceByDate(NumberSourceByDayInput input)
    {
        return await Context.Queryable<NS_GroupSource>()
                .InnerJoin<NS_TimeSlot>((source, time) => time.Id == source.TimeSlotID)
                .InnerJoin<NS_TimeSlotEntry>((source, time, timeEnrtry) => timeEnrtry.Id == time.TimeSlotEntryID)
                .Where(source => source.UnitCode == input.UnitCode)
                .Where(source => source.Date.ToString("yyyy-MM-dd") == (input.Year + "-" + input.Month + "-" + input.Day))
                .Select<NumberSourceOutput>()
                .ToArrayAsync();
    }

    /// <summary>
    /// 获取重点号源某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetKeyProjectNumberSourceByDate(NumberSourceByDayInput input)
    {
        return await Context.Queryable<NS_KeyProjectSource>()
                .InnerJoin<NS_TimeSlot>((source, time) => time.Id == source.TimeSlotID)
                .InnerJoin<NS_TimeSlotEntry>((source, time, timeEnrtry) => timeEnrtry.Id == time.TimeSlotEntryID)
                .Where(source => source.CodeItemCode == input.CodeItemCode)
                .Where(source => source.Date.ToString("yyyy-MM-dd") == (input.Year + "-" + input.Month + "-" + input.Day))
                .Select<NumberSourceOutput>()
                .ToArrayAsync();
    }

    /// <summary>
    /// 编辑号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> EditNumberSource(List<EditNumberSourceInput> input)
    {
        List<NS_SourceTypes> sourceTypes = Context.Queryable<NS_SourceTypes>().ToList();//获取全部号源类型
        foreach (var item in input)//遍历入参
        {
            //判断号源类型
            NS_SourceTypes sourceType = sourceTypes.First(x => x.Id == item.SourceTypeID);
            if (sourceType == null)
            {
                await Unify.SetError($"{item.SourceTypeID}号源类型无效，请检查！");
                return false;
            }
            switch (sourceType.SourceTypeTable)
            {
                case "NS_GlobalSource":
                    bool isSuccessByGlobal = EdidGlobalSource(item, out string msg);
                    if (!isSuccessByGlobal)
                    {
                        Unify.SetError(msg);
                        return isSuccessByGlobal;
                    }
                    continue;
                case "NS_IndividualSource":
                    bool isSuccessByIndividual = EdidIndividualSource(item, out string personMsg);
                    if (!isSuccessByIndividual)
                    {
                        Unify.SetError(personMsg);
                        return isSuccessByIndividual;
                    }
                    continue;
                case "NS_GroupSource":
                    bool isSuccessByGroup = EdidGroupSource(item, out string groupMsg);
                    if (!isSuccessByGroup)
                    {
                        Unify.SetError(groupMsg);
                        return isSuccessByGroup;
                    }
                    continue;
                case "NS_ResidualSource":
                    bool isSuccessByResidual = EdidResidualSource(item, out string residualMsg);
                    if (!isSuccessByResidual)
                    {
                        Unify.SetError(residualMsg);
                        return isSuccessByResidual;
                    }
                    continue;
                case "NS_KeyProjectSource":
                    bool isSuccessByKeyProject = EdidKeyProjectSource(item, out string keyProjectMsg);
                    if (!isSuccessByKeyProject)
                    {
                        Unify.SetError(keyProjectMsg);
                        return isSuccessByKeyProject;
                    }
                    continue;
                default:
                    break;
                    // TODO:第三方实现
            }
        }
        return true;
    }

    /// <summary>
    /// 删除/清空号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> DeleteNumberSource(DeleteNumberSourceInput input)
    {
        DateTime times = Convert.ToDateTime(input.StartTime);
        int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;
        for (int i = 0; i <= nums; i++)
        {
            var bookdate = times.AddDays(i).ToString("yyyy-MM-dd");
            var bookNoList = Context.Queryable<NS_GlobalSource>().Where(x => x.Date.ToString("yyyy-MM-dd") == bookdate).ToList();
            foreach (var item in bookNoList)
            {
                if (item.UsedCapacity == 0)
                {
                    Context.Deleteable(item);
                }
                else
                {
                    await Unify.SetError($"{bookdate}已有{item.UsedCapacity}人预约，禁止清除");
                    return false;
                }
            }
        }
        return true;
    }

    /// <summary>
    /// 设置号源休假
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> SetNumberSourceVacation(List<SetNumberSourceVacationInput> input)
    {
        foreach (var item in input)
        {
            DateTime times = Convert.ToDateTime(item.StartTime);
            int nums = Convert.ToDateTime(item.EndTime).Subtract(Convert.ToDateTime(item.StartTime)).Days;

            for (int i = 0; i <= nums; i++)
            {
                var bookdate = times.AddDays(i).ToString("yyyy-MM-dd");
                var result = Context.Queryable<NS_GlobalSource>().First(n => n.Date.ToString("yyyy-MM-dd") == bookdate && n.TimeSlotID.Contains(item.TimeSlotID));
                if (result == null)
                {
                    int isoWeekNumber = ((int)times.AddDays(i).DayOfWeek + 6) % 7 + 1;
                    NS_GlobalSource booksum = new NS_GlobalSource();
                    booksum.TimeSlotID = item.TimeSlotID;
                    booksum.Date = times.AddDays(i);
                    booksum.Week = isoWeekNumber;
                    booksum.TotalCapacity = 0;
                    booksum.AvailableCapacity = 0;
                    booksum.IsVacation = item.Statu;
                    Context.Insertable(booksum);
                }
                else
                {
                    //已预约不能设置休假
                    if (item.Statu == "T" && result.UsedCapacity > 0)
                    {
                        await Unify.SetError($"日期:{result.Date.ToString("yyyy-MM-dd")},时段：{result.TimeSlotID}号源已使用不能设置休假!");
                        return false;
                    }
                    result.IsVacation = item.Statu;
                    Context.Updateable(result);
                }
            }
        }
        return true;
    }

    /// <summary>
    /// 检查当前时段是否和号源类型有关联
    /// </summary>
    /// <param name="sourceTypeID"></param>
    /// <param name="timeSlotID"></param>
    /// <returns></returns>
    private bool CheckAssociations(string sourceTypeID, string timeSlotID)
    {
        NS_TimeSlot timeSlot = Context.Queryable<NS_TimeSlot>().First(x => x.Id == timeSlotID);
        if (timeSlot != null)
        {
            return timeSlot.SourceTypeID == sourceTypeID;
        }
        return false;
    }

    /// <summary>
    /// 检查全局号源中和要修改的号源时段是否存在
    /// </summary>
    /// <param name="timeSlotID"></param>
    /// <returns></returns>
    private bool CheckGlobalIsIncludeSlot(string timeSlotID)
    {
        List<NS_TimeSlot> timeSlots = Context.Queryable<NS_TimeSlot>().ToList();//获取全部时段关联信息
        var globalTimeSlotEntryIDs = timeSlots.Where(x => x.SourceTypeID == "464564896820001").Select(x => x.TimeSlotEntryID).ToArray();
        NS_TimeSlot checkSlot = timeSlots.First(x => x.Id == timeSlotID);//待检查时段的关联信息
        if (checkSlot != null)
        {
            return globalTimeSlotEntryIDs.Contains(checkSlot.TimeSlotEntryID);
        }
        return false;
    }

    /// <summary>
    /// 消费全局号源
    /// </summary>
    /// <param name="isAddNumber"></param>
    /// <param name="timeSlotID"></param>
    /// <param name="date"></param>
    /// <param name="total"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    private bool GlobalConsumption(string timeSlotID, DateTime date, int total, out string msg)
    {
        msg = "成功";
        List<NS_TimeSlot> timeSlots = Context.Queryable<NS_TimeSlot>().ToList();//获取全部时段关联信息
        NS_TimeSlot checkSlot = timeSlots.First(x => x.Id == timeSlotID);//待检查时段的关联信息
        if (checkSlot != null)
        {
            NS_TimeSlot globalTimeSlot = timeSlots.First(x => x.SourceTypeID == "464564896820001" && x.TimeSlotEntryID == checkSlot.TimeSlotEntryID);
            if (globalTimeSlot != null)
            {
                NS_GlobalSource globalSource = Context.Queryable<NS_GlobalSource>().First(x => x.Date == date && x.TimeSlotID == globalTimeSlot.Id);
                if (globalSource == null)
                {
                    msg = "请先设置全局号源";
                    return false;
                }
                if (globalSource.AvailableCapacity - total < 0)
                {
                    msg = "总号源不足本次抵扣！";
                    return false;
                }
                globalSource.AvailableCapacity -= total;
                globalSource.UsedCapacity += total;
                if (globalSource.AvailableCapacity < 0 || globalSource.UsedCapacity < 0)
                {
                    msg = "触发号源非负异常！";
                    return false;
                }
                if (Context.Updateable(globalSource).ExecuteCommand() > 0)
                {
                    return true;
                }
            }
        }
        return false;
    }

    /// <summary>
    /// 编辑全局号源
    /// </summary>
    /// <param name="input"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    private bool EdidGlobalSource(EditNumberSourceInput input, out string msg)
    {
        msg = "成功";
        try
        {
            DateTime starTime = Convert.ToDateTime(input.StartTime);//转换开始时间
            int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;//计算开始时间和结束时间的日期差
            for (int i = 0; i <= nums; i++)//循坏日期差
            {
                var bookdate = Convert.ToDateTime(starTime.AddDays(i).ToString("yyyy-MM-dd"));//设置从开始时间到结束时间的指定时段的号源
                var result = Context.Queryable<NS_GlobalSource>().First(x => x.Date == bookdate && x.TimeSlotID == input.TimeSlotID);//从数据库中查询当前日期的当前时段是否已经存在
                if (result == null)//如果不存在，则新增
                {
                    int weekNumber = ((int)Convert.ToDateTime(bookdate).DayOfWeek + 6) % 7 + 1;
                    NS_GlobalSource sum = new NS_GlobalSource();
                    sum.Date = bookdate;
                    sum.TotalCapacity = input.TotalCapacity;
                    sum.AvailableCapacity = input.TotalCapacity;
                    sum.UsedCapacity = 0;
                    sum.TimeSlotID = input.TimeSlotID;
                    sum.IsVacation = input.Statu;
                    sum.Week = weekNumber;
                    if (Context.Insertable(sum).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
                else//存在则修改
                {
                    if (result.IsVacation == "T" && nums > 0)
                    {
                        continue;
                    }
                    else
                    {
                        result.IsVacation = input.Statu;
                        result.AvailableCapacity += input.TotalCapacity - result.TotalCapacity;
                        result.TotalCapacity = input.TotalCapacity;
                    }
                    if (Context.Updateable(result).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
    }

    /// <summary>
    /// 编辑个检号源
    /// </summary>
    /// <param name="input"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    private bool EdidIndividualSource(EditNumberSourceInput input, out string msg)
    {
        msg = "成功";
        try
        {
            //判断当前时段是否和号源类型有关联
            if (!CheckAssociations(input.SourceTypeID, input.TimeSlotID))
            {
                msg = $"{input.SourceTypeID}和{input.TimeSlotID}无关联，请检查后再试！";
                return false;
            }
            //判断全局时段中是否包含当前时段
            if (!CheckGlobalIsIncludeSlot(input.TimeSlotID))
            {
                msg = $"全局号源时段中不存在{input.TimeSlotID}，请检查后再试！";
                return false;
            }
            DateTime starTime = Convert.ToDateTime(input.StartTime);//转换开始时间
            int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;//计算开始时间和结束时间的日期差
            for (int i = 0; i <= nums; i++)//循坏日期差
            {
                var bookdate = Convert.ToDateTime(starTime.AddDays(i).ToString("yyyy-MM-dd"));//设置从开始时间到结束时间的指定时段的号源
                var result = Context.Queryable<NS_IndividualSource>().First(x => x.Date == bookdate && x.TimeSlotID == input.TimeSlotID && x.SourceTypeID == input.SourceTypeID);//从数据库中查询当前日期的当前时段是否已经存在
                if (result == null)//如果不存在，则新增
                {
                    //检查和操作全局号源
                    if (!GlobalConsumption(input.TimeSlotID, bookdate, input.TotalCapacity, out msg))
                    {
                        return false;
                    }
                    int weekNumber = ((int)Convert.ToDateTime(bookdate).DayOfWeek + 6) % 7 + 1;
                    NS_IndividualSource sum = new NS_IndividualSource();
                    sum.SourceTypeID = input.SourceTypeID;
                    sum.Date = bookdate;
                    sum.TotalCapacity = input.TotalCapacity;
                    sum.AvailableCapacity = input.TotalCapacity;
                    sum.UsedCapacity = 0;
                    sum.TimeSlotID = input.TimeSlotID;
                    sum.IsVacation = input.Statu;
                    sum.Week = weekNumber;
                    if (Context.Insertable(sum).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
                else//存在则修改
                {
                    if (result.IsVacation == "T" && nums > 0)
                    {
                        continue;
                    }
                    else
                    {
                        if (!GlobalConsumption(input.TimeSlotID, bookdate, input.TotalCapacity - result.TotalCapacity, out msg))
                        {
                            return false;
                        }
                        result.IsVacation = input.Statu;
                        result.AvailableCapacity += input.TotalCapacity - result.TotalCapacity;
                        result.TotalCapacity = input.TotalCapacity;
                    }
                    if (Context.Updateable(result).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
    }

    /// <summary>
    /// 修改团体号源
    /// </summary>
    /// <param name="input"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool EdidGroupSource(EditNumberSourceInput input, out string msg)
    {
        msg = "成功";
        try
        {
            //判断当前时段是否和号源类型有关联
            if (!CheckAssociations(input.SourceTypeID, input.TimeSlotID))
            {
                msg = $"{input.SourceTypeID}和{input.TimeSlotID}无关联，请检查后再试！";
                return false;
            }
            //判断全局时段中是否包含当前时段
            if (!CheckGlobalIsIncludeSlot(input.TimeSlotID))
            {
                msg = $"全局号源时段中不存在{input.TimeSlotID}，请检查后再试！";
                return false;
            }
            DateTime starTime = Convert.ToDateTime(input.StartTime);//转换开始时间
            int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;//计算开始时间和结束时间的日期差
            for (int i = 0; i <= nums; i++)//循坏日期差
            {
                var bookdate = Convert.ToDateTime(starTime.AddDays(i).ToString("yyyy-MM-dd"));//设置从开始时间到结束时间的指定时段的号源
                var result = Context.Queryable<NS_GroupSource>().First(x => x.Date == bookdate && x.TimeSlotID == input.TimeSlotID && x.UnitCode == input.ComanyCode);//从数据库中查询当前日期的当前时段是否已经存在
                if (result == null)//如果不存在，则新增
                {
                    //检查和操作全局号源
                    if (!GlobalConsumption(input.TimeSlotID, bookdate, input.TotalCapacity, out msg))
                    {
                        return false;
                    }
                    int weekNumber = ((int)Convert.ToDateTime(bookdate).DayOfWeek + 6) % 7 + 1;
                    NS_GroupSource sum = new NS_GroupSource();
                    sum.UnitCode = input.ComanyCode;
                    sum.Date = bookdate;
                    sum.TotalCapacity = input.TotalCapacity;
                    sum.AvailableCapacity = input.TotalCapacity;
                    sum.UsedCapacity = 0;
                    sum.TimeSlotID = input.TimeSlotID;
                    sum.IsVacation = input.Statu;
                    sum.Week = weekNumber;
                    if (Context.Insertable(sum).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
                else//存在则修改
                {
                    if (result.IsVacation == "T" && nums > 0)
                    {
                        continue;
                    }
                    else
                    {
                        if (!GlobalConsumption(input.TimeSlotID, bookdate, input.TotalCapacity - result.TotalCapacity, out msg))
                        {
                            return false;
                        }
                        result.IsVacation = input.Statu;
                        result.AvailableCapacity += input.TotalCapacity - result.TotalCapacity;
                        result.TotalCapacity = input.TotalCapacity;
                    }
                    if (Context.Updateable(result).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
    }

    /// <summary>
    /// 修改补检号源
    /// </summary>
    /// <param name="input"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool EdidResidualSource(EditNumberSourceInput input, out string msg)
    {
        msg = "成功";
        try
        {
            //判断当前时段是否和号源类型有关联
            if (!CheckAssociations(input.SourceTypeID, input.TimeSlotID))
            {
                msg = $"{input.SourceTypeID}和{input.TimeSlotID}无关联，请检查后再试！";
                return false;
            }
            //判断全局时段中是否包含当前时段
            if (!CheckGlobalIsIncludeSlot(input.TimeSlotID))
            {
                msg = $"全局号源时段中不存在{input.TimeSlotID}，请检查后再试！";
                return false;
            }
            DateTime starTime = Convert.ToDateTime(input.StartTime);//转换开始时间
            int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;//计算开始时间和结束时间的日期差
            for (int i = 0; i <= nums; i++)//循坏日期差
            {
                var bookdate = Convert.ToDateTime(starTime.AddDays(i).ToString("yyyy-MM-dd"));//设置从开始时间到结束时间的指定时段的号源
                var result = Context.Queryable<NS_ResidualSource>().First(x => x.Date == bookdate && x.TimeSlotID == input.TimeSlotID);//从数据库中查询当前日期的当前时段是否已经存在
                if (result == null)//如果不存在，则新增
                {
                    //检查和操作全局号源
                    if (!GlobalConsumption(input.TimeSlotID, bookdate, input.TotalCapacity, out msg))
                    {
                        return false;
                    }
                    int weekNumber = ((int)Convert.ToDateTime(bookdate).DayOfWeek + 6) % 7 + 1;
                    NS_ResidualSource sum = new NS_ResidualSource();
                    sum.Date = bookdate;
                    sum.TotalCapacity = input.TotalCapacity;
                    sum.AvailableCapacity = input.TotalCapacity;
                    sum.UsedCapacity = 0;
                    sum.TimeSlotID = input.TimeSlotID;
                    sum.IsVacation = input.Statu;
                    sum.Week = weekNumber;
                    if (Context.Insertable(sum).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
                else//存在则修改
                {
                    if (result.IsVacation == "T" && nums > 0)
                    {
                        continue;
                    }
                    else
                    {
                        if (!GlobalConsumption(input.TimeSlotID, bookdate, input.TotalCapacity - result.TotalCapacity, out msg))
                        {
                            return false;
                        }
                        result.IsVacation = input.Statu;
                        result.AvailableCapacity += input.TotalCapacity - result.TotalCapacity;
                        result.TotalCapacity = input.TotalCapacity;
                    }
                    if (Context.Updateable(result).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
    }

    /// <summary>
    /// 修改重点项目号源
    /// </summary>
    /// <param name="input"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool EdidKeyProjectSource(EditNumberSourceInput input, out string msg)
    {
        msg = "成功";
        try
        {
            //判断当前时段是否和号源类型有关联
            if (!CheckAssociations(input.SourceTypeID, input.TimeSlotID))
            {
                msg = $"{input.SourceTypeID}和{input.TimeSlotID}无关联，请检查后再试！";
                return false;
            }
            //判断全局时段中是否包含当前时段
            if (!CheckGlobalIsIncludeSlot(input.TimeSlotID))
            {
                msg = $"全局号源时段中不存在{input.TimeSlotID}，请检查后再试！";
                return false;
            }
            DateTime starTime = Convert.ToDateTime(input.StartTime);//转换开始时间
            int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;//计算开始时间和结束时间的日期差
            for (int i = 0; i <= nums; i++)//循坏日期差
            {
                var bookdate = Convert.ToDateTime(starTime.AddDays(i).ToString("yyyy-MM-dd"));//设置从开始时间到结束时间的指定时段的号源
                var result = Context.Queryable<NS_KeyProjectSource>().First(x => x.Date == bookdate && x.TimeSlotID == input.TimeSlotID && x.CodeItemCode == input.CodeItemCode);//从数据库中查询当前日期的当前时段是否已经存在
                if (result == null)//如果不存在，则新增
                {
                    int weekNumber = ((int)Convert.ToDateTime(bookdate).DayOfWeek + 6) % 7 + 1;
                    NS_KeyProjectSource sum = new NS_KeyProjectSource();
                    sum.CodeItemCode = input.ComanyCode;
                    sum.Date = bookdate;
                    sum.TotalCapacity = input.TotalCapacity;
                    sum.AvailableCapacity = input.TotalCapacity;
                    sum.UsedCapacity = 0;
                    sum.TimeSlotID = input.TimeSlotID;
                    sum.IsVacation = input.Statu;
                    sum.Week = weekNumber;
                    if (Context.Insertable(sum).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
                else//存在则修改
                {
                    if (result.IsVacation == "T" && nums > 0)
                    {
                        continue;
                    }
                    else
                    {
                        result.IsVacation = input.Statu;
                        result.AvailableCapacity += input.TotalCapacity - result.TotalCapacity;
                        result.TotalCapacity = input.TotalCapacity;
                    }
                    if (Context.Updateable(result).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
    }

    /// <summary>
    /// 获取单位补检信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<CompanyResidualOutput> GetCompanyResidual(CompanyResidualInput input)
    {
        CompanyResidualOutput data = new CompanyResidualOutput();
        try
        {
            CompanyResidualSource source = await Context.Queryable<CompanyResidualSource>().FirstAsync(x => x.CompanyCode == input.CompanyCode && x.CompanyName == x.CompanyName);
            if (source == null)
            {
                data = input.Adapt<CompanyResidualOutput>();
                return data;
            }
            data = source.Adapt<CompanyResidualOutput>();
            return data;
        }
        catch (Exception ex)
        {
            Unify.SetError("获取单位补检信息失败!" + ex.Message);
            return data;
        }
    }

    /// <summary>
    /// 编辑单位补检生效信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> EditCompanyResidual(EditCompanyResidualInput input)
    {
        try
        {
            CompanyResidualSource source = await Context.Queryable<CompanyResidualSource>().FirstAsync(x => x.CompanyCode == input.CompanyCode && x.CompanyName == x.CompanyName);
            if (source == null)
            {
                CompanyResidualSource data = input.Adapt<CompanyResidualSource>();
                if (Context.Insertable(data).ExecuteCommand() < 0)
                {
                    return false;
                }
            }
            else
            {
                source.StartDate = input.StartDate;
                source.EndDate = input.EndDate;
                source.OpenDate = input.OpenDate;
                source.IsEnabled = input.IsEnabled;
                source.SetCount++;
                if (Context.Updateable(source).ExecuteCommand() < 0)
                {
                    return false;
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            Unify.SetError("编辑单位补检生效信息失败!" + ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 获取微信单位号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<NumberSourceOutput>> GetWxGroupNumberSource(NumberSourceInput input)
    {
        var startDate = DateTime.Parse($"{input.Year}-{input.Month}-01");
        var endData = startDate.AddMonths(1);
        // 获取某月多少天
        var totalDays = DateTime.DaysInMonth(startDate.Year, startDate.Month);

        // 查询团体号源
        var groupSource = await Context.Queryable<NS_GroupSource>()
                        .Where(x => x.UnitCode == input.UnitCode && SqlFunc.Between(x.Date, startDate, endData))
                        .GroupBy(x => new { x.Date, x.IsVacation, x.Week })
                        .Select(x => new NumberSourceOutput
                        {
                            Date = x.Date,
                            TotalCapacity = SqlFunc.AggregateSumNoNull(x.TotalCapacity),
                            UsedCapacity = SqlFunc.AggregateSumNoNull(x.UsedCapacity),
                            AvailableCapacity = SqlFunc.AggregateSumNoNull(x.AvailableCapacity),
                            Week = x.Week,
                            IsVacation = x.IsVacation,
                            IsResidual = false
                        })
                        .ToListAsync();

        // 查询单位有无补检数据
        var companyResidual = await Context.Queryable<CompanyResidualSource>().FirstAsync(x => x.CompanyCode == input.UnitCode);

        Dictionary<DateTime, NumberSourceOutput> residualDict = [];
        if (companyResidual?.IsEnabled == "T")
        {
            var residualSource = await Context.Queryable<NS_ResidualSource>()
                          .Where(x => SqlFunc.Between(x.Date, startDate, endData))
                          .GroupBy(x => new { x.Date, x.IsVacation, x.Week })
                         .Select(x => new NumberSourceOutput
                         {
                             Date = x.Date,
                             TotalCapacity = SqlFunc.AggregateSumNoNull(x.TotalCapacity),
                             UsedCapacity = SqlFunc.AggregateSumNoNull(x.UsedCapacity),
                             AvailableCapacity = SqlFunc.AggregateSumNoNull(x.AvailableCapacity),
                             Week = x.Week,
                             IsVacation = x.IsVacation,
                             IsResidual = true
                         })
                        .ToListAsync();

            if (residualSource.Count != 0)
                residualDict = residualSource.ToDictionary(x => x.Date, x => x);
        }

        var sourceData = new List<NumberSourceOutput>();
        for (int i = 0; i < totalDays; i++)
        {
            var source = groupSource.FirstOrDefault(x => x.Date == startDate.AddDays(i));
            if (source != null && source.AvailableCapacity > 0)
            {
                sourceData.Add(source);
                continue;
            }

            if (residualDict.TryGetValue(startDate.AddDays(i), out var residual))
                sourceData.Add(residual);
        }

        return sourceData;
    }

    /// <summary>
    /// 获取微信单位某天的号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<NumberSourceOutput>> GetWxGroupNumberSourceByDate(NumberSourceByDayInput input)
    {
        var startDate = DateTime.Parse($"{input.Year}-{input.Month}-{input.Day}");
        // 查询团体号源

        //var groupSource = await GetGroupNumberSourceByDate(input);
        var groupSource = await Context.Queryable<NS_GroupSource>()
                        .InnerJoin<NS_TimeSlot>((source, time) => time.Id == source.TimeSlotID)
                        .InnerJoin<NS_TimeSlotEntry>((source, time, timeEnrtry) => timeEnrtry.Id == time.TimeSlotEntryID)
                        .Where(source => source.UnitCode == input.UnitCode && source.Date == startDate)
                        .Select((source, time, timeEnrtry) => new NumberSourceOutput
                        {
                            Date = source.Date,
                            TimeSlotID = time.Id,
                            TimeSlotName = timeEnrtry.TimeSlotName,
                            TotalCapacity = source.TotalCapacity,
                            UsedCapacity = source.UsedCapacity,
                            AvailableCapacity = source.AvailableCapacity,
                            Week = source.Week,
                            IsVacation = source.IsVacation,
                            IsResidual = false
                        })
                        .ToListAsync();

        // 查询单位有无补检数据
        var companyResidual = await Context.Queryable<CompanyResidualSource>().FirstAsync(x => x.CompanyCode == input.UnitCode);

        Dictionary<string, NumberSourceOutput> residualDict = [];
        var sourceData = new List<NumberSourceOutput>();
        if (companyResidual?.IsEnabled == "T")
        {
            var residualSource = await GetResidualNumberSourceByDate(input);
            //var residualSource = await Context.Queryable<NS_ResidualSource>()
            //              .Where(x => x.Date == startDate)
            //             .Select(x => new NumberSourceOutput
            //             {
            //                 Date = x.Date,
            //                 TimeSlotID = x.TimeSlotID,
            //                 TotalCapacity = x.TotalCapacity,
            //                 UsedCapacity = x.UsedCapacity,
            //                 AvailableCapacity = x.AvailableCapacity,
            //                 Week = x.Week,
            //                 IsVacation = x.IsVacation,
            //                 IsResidual = true
            //             })
            //            .ToListAsync();
            if (residualSource.Length != 0)
                residualDict = residualSource.ToDictionary(x => x.TimeSlotName, x => x);
            if (groupSource.Count > 0)
            {
                foreach (var item in groupSource)
                {
                    if (item != null)
                    {
                        if (item.AvailableCapacity > 0)
                        {
                            sourceData.Add(item);
                        }
                        else
                        {
                            if (residualDict.TryGetValue(item.TimeSlotName, out var residual))
                                sourceData.Add(residual);
                        }
                    }

                }

            }
            else
            {
                sourceData = residualSource.Adapt<List<NumberSourceOutput>>();
            }
        }
        return sourceData;
    }

    #endregion

    /// <summary>
    /// 更新个检号源
    /// </summary>
    /// <param name="beginTime"></param>
    /// <param name="sourceTypeID"></param>
    /// <param name="timeSlotID"></param>
    /// <returns></returns>
    public async Task<bool> UpdatePersonNumberSource(DateTime beginTime, string sourceTypeID, string timeSlotID)
    {
        try
        {
            //查询符合条件的订单数量
            var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.正在缴费, OrderStatus.待支付, OrderStatus.已支付, OrderStatus.已导入 };
            var ordersCount = await Context.Queryable<PersonOrder>()
                .CountAsync(x => x.BeginTime == beginTime && x.SourceTypeID == sourceTypeID && x.TimeSlotID == timeSlotID && SqlFunc.ContainsArray(statusArray, x.Status));

            //获取当前时间段号源数
            var tableName = Context.Queryable<NS_SourceTypes>().First(x => x.Id == sourceTypeID)?.SourceTypeTable;
            var source = await Context.Queryable<NS_IndividualSource>().AS(tableName)
                        .Where(x => x.Date == beginTime && x.SourceTypeID == sourceTypeID && x.TimeSlotID == timeSlotID)
                        .FirstAsync();

            if (source == null)
            {
                Unify.SetError("号源数据不存在!");
                return false;
            }

            source.UsedCapacity = ordersCount;// 已用容量
            var availableCapacity = source.TotalCapacity - source.UsedCapacity;
            source.AvailableCapacity = availableCapacity > 0 ? availableCapacity : 0;
            return await Context.Updateable(source).ExecuteCommandAsync() > 0;
        }
        catch (Exception ex)
        {
            Unify.SetError(ex.Message);
            _logger.LogError(ex, "UpdatePersonNumberSource:{0}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 更新团检号源
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="beginTime">体检时间</param>
    /// <param name="timeSlotID">时段Id</param>
    /// <param name="isResidual">补检标识</param>
    /// <returns></returns>
    public async Task<bool> UpdateGroupNumberSource(string companyCode, DateTime beginTime, string timeSlotID, bool isResidual)
    {
        try
        {
            //查询符合条件的订单数量
            var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.正在缴费, OrderStatus.待支付, OrderStatus.已支付, OrderStatus.已导入 };

            var ordersCount = Context.Queryable<TeamOrder>()
                    .WhereIF(isResidual, x => x.IsResidual == isResidual)
                    .WhereIF(!isResidual, x => x.IsResidual == isResidual && x.CompanyCode == companyCode)
                    .Where(x => x.BeginTime == beginTime && x.TimeSlotID == timeSlotID && SqlFunc.ContainsArray(statusArray, x.Status))
                    .Count();

            dynamic source = isResidual
                            ? await Context.Queryable<NS_ResidualSource>().FirstAsync(x => x.Date == beginTime && x.TimeSlotID == timeSlotID)
                            : await Context.Queryable<NS_GroupSource>().FirstAsync(x => x.UnitCode == companyCode && x.Date == beginTime && x.TimeSlotID == timeSlotID);

            if (source == null)
            {
                Unify.SetError("号源数据不存在!");
                return false;
            }

            source.UsedCapacity = ordersCount;// 已用容量
            var availableCapacity = source.TotalCapacity - source.UsedCapacity;
            source.AvailableCapacity = availableCapacity > 0 ? availableCapacity : 0;

            int affected = isResidual
                           ? await Context.Updateable<NS_ResidualSource>(source).ExecuteCommandAsync()
                           : await Context.Updateable<NS_GroupSource>(source).ExecuteCommandAsync();

            return affected > 0;
        }
        catch (Exception ex)
        {
            Unify.SetError(ex.Message);
            _logger.LogError(ex, "UpdateGroupNumberSource:{0}", ex.Message);
            return false;
        }
    }
}