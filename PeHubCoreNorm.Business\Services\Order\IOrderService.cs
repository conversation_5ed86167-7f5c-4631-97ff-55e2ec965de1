﻿using PeHubCoreNorm.Business.Input;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单业务接口
/// </summary>
public interface IOrderService : ITransient, ITenantDBTransient
{
    /// <summary>
    /// 添加个检订单流程
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> AddPersonOrderProcess(PersonOrderInput input);


    /// <summary>
    /// 创建单位订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<TeamOrder> CreateTeamOrder(TeamOrder input);


    /// <summary>
    /// 取消单位订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CanceleTeamOrder(TeamOrder input);


    /// <summary>
    /// 改期单位订单
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    Task<bool> RescheduleTeamOrder(TeamOrder order);



    /// <summary>
    /// 取消个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CancelPersonOrder(CancelOrder input);

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<List<OrderList>> GetOrderList(OrderQuery query);



    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query);

    /// <summary>
    /// 处理超时订单
    /// </summary>
    /// <returns></returns>
    Task TimeOutOrder();

    /// <summary>
    /// 同步订单
    /// </summary>
    /// <returns></returns>
    Task SyncOrder();

    /// <summary>
    /// 获取个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<OrderList>> GetPersonOrderList(PersonOrderPageInput input);

    /// <summary>
    /// 获取个检订单
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<TeamOrder>> GetTeamOrderList(TeamOrderPageQueryInput query);

}
