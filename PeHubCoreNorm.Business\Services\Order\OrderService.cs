using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Services.PhysicalExamService;
using System.Linq.Expressions;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单服务
/// </summary>
public class OrderService : BizDbRepository<PersonOrder>, IOrderService
{
    private readonly ILogger<OrderService> _logger;
    private readonly INumberSourceService _numberSourceService;
    private readonly ISyncService _syncService;
    private readonly IPhysicalExamService _physicalExamService;
    private readonly IUnitPersonnelListService _unitPersonnelListService;

    public OrderService(
        ILogger<OrderService> logger,
        INumberSourceService numberSourceService,
        ISyncService syncService,
        IPhysicalExamService physicalExamService,
        IUnitPersonnelListService unitPersonnelListService)
    {
        _logger = logger;
        _numberSourceService = numberSourceService;
        _syncService = syncService;
        _physicalExamService = physicalExamService;
        _unitPersonnelListService = unitPersonnelListService;
    }

    /// <summary>
    /// 添加个检订单流程
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> AddPersonOrderProcess(PersonOrderInput input)
    {
        // 预约前检查数据
        if (!await CheckAddPersonOrderData(input))
            return false;

        // 添加订单
        if (!await AddPersonOrder(input))
            return false;

        // 更新号源
        if (!await _numberSourceService.UpdatePersonNumberSource(input.BeginTime, input.SourceTypeID, input.TimeSlotID))
            return false;

        return true;
    }

    /// <summary>
    /// 取消个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CancelPersonOrder(CancelOrder input)
    {
        try
        {
            var order = await Context.Queryable<PersonOrder>().FirstAsync(x => x.Id == input.OrderId && x.MembersId == input.MemberId);
            if (order == null)
            {
                Unify.SetError("订单不存在，请返回订单页面重新查询");
                return false;
            }

            var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.待支付 };
            if (!statusArray.Contains(order.Status))
            {
                Unify.SetError("订单状态已变更,请刷新页面查看最新状态!");
                return false;
            }

            // 允许取消订单的最晚时间是体检前1天18:00
            var cancelDeadline = order.BeginTime.Date.AddDays(-1).AddHours(18);
            if (DateTime.Now >= cancelDeadline)
            {
                Unify.SetError("已过有效取消时间（体检前一日18:00前），无法取消");
                return false;
            }

            #region 查询体检系统支付状态
            var result = await GetPhysicalExamPayStatus(order);
            if (result.ResultCode != 1)
            {
                order.Status = OrderStatus.异常订单;
                order.ErrorMsg = "查询订单失败";
                await Context.Updateable(order).ExecuteCommandAsync();
            }

            if (result.PayFlag)
            {
                order.Status = OrderStatus.已支付;
                await Context.Updateable(order).ExecuteCommandAsync();
                Unify.SetError("订单状态已改变,暂不支持当前操作!");
                return false;
            }
            #endregion

            switch (order.Status)
            {
                case OrderStatus.已预约:
                case OrderStatus.待支付:
                    order.Status = OrderStatus.已取消;
                    order.EndTime = DateTime.Now;
                    break;
                case OrderStatus.已支付:
                    // 退款处理（封装单独方法）
                    break;
            }


            await Context.Updateable(order).ExecuteCommandAsync();

            // 更新号源
            if (!await _numberSourceService.UpdatePersonNumberSource(order.BeginTime, order.SourceTypeID, order.TimeSlotID))
                return false;

            #region 删除体检系统记录
            if (!await DeletePhysicalExamOrder(order))
                return false;
            #endregion

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("CancelPersonOrder:{0}", ex.Message);
            Unify.SetError("取消订单失败,请联系工作人员!");
            return false;
        }
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderList>> GetOrderList(OrderQuery query)
    {
        return query.OrderType switch
        {
            0 => await GetAllOrders(query.MemberId),
            1 => await GetPersonOrder(query.MemberId),
            2 => await GetGroupOrder(query.MemberId),
            3 => await GetPayRecord(query.MemberId),
            _ => Unify.SetError("无效的查询订单类型")
        };

        #region 内部方法(获取所有类型订单)
        async Task<List<OrderList>> GetAllOrders(string memberId)
        {
            var result = new List<OrderList>();
            result.AddRange(await GetPersonOrder(memberId));
            result.AddRange(await GetGroupOrder(memberId));
            result.AddRange(await GetPayRecord(memberId));

            return result;
        }
        #endregion
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query)
    {
        return await Context.Queryable<OrderDetail>()
            .Where(x => x.OrderId == query.OrderId)
            .ToListAsync();
    }

    /// <summary>
    /// 创建单位订单
    /// </summary>
    /// <param name="input">创建订单输入</param>
    /// <returns>创建的订单详情</returns>
    public async Task<TeamOrder> CreateTeamOrder(TeamOrder input)
    {


        input.Status = OrderStatus.已预约;
        TeamOrder teamOrder = await Context.Insertable(input).ExecuteReturnEntityAsync();

        await _numberSourceService.UpdateGroupNumberSource(input.CompanyCode, input.BeginTime, input.TimeSlotID, input.IsResidual);

        await _unitPersonnelListService.AlreadyUsed(
       p => p.CompanyCode == input.CompanyCode && p.BatchNumber==input.CompanyTimes && p.IdNumber==input.CardNo && p.PackAgeCode==input.ClusCode,
       p => new UnitPersonnelList
       {
           Status = 2, // 更新为已使用状态
           ModifyDate = DateTime.Now
       });


        return teamOrder;

    }


    /// <summary>
    /// 取消团检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CanceleTeamOrder(TeamOrder order)
    {
        var reslut = await Context.Updateable(order)
              .SetColumns(x => x.Status == OrderStatus.已取消)
              .ExecuteCommandAsync();
        await _numberSourceService.UpdatePersonNumberSource(order.BeginTime, order.SourceTypeID, order.TimeSlotID);
        return reslut > 0;
    }

    /// <summary>
    /// 改期团检订单
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    public async Task<bool> RescheduleTeamOrder(TeamOrder order)
    {
        var reslut = await Context.Updateable(order)
              .ExecuteCommandAsync();
        await _numberSourceService.UpdatePersonNumberSource(order.BeginTime, order.SourceTypeID, order.TimeSlotID);
        return reslut > 0;
    }


    /// <summary>
    /// 验证团检订单输入数据
    /// </summary>
    /// <param name="input">输入数据</param>
    /// <returns>验证结果</returns>
    private async Task<bool> ValidateTeamOrderInput(AddTeamOrderInput input)
    {
        // 1. 基本字段验证
        if (string.IsNullOrEmpty(input.Name))
        {
            Unify.SetError("姓名不能为空");
            return false;
        }

        if (string.IsNullOrEmpty(input.CardNo))
        {
            Unify.SetError("证件号不能为空");
            return false;
        }

        if (string.IsNullOrEmpty(input.Tel))
        {
            Unify.SetError("手机号不能为空");
            return false;
        }

        if (string.IsNullOrEmpty(input.CompanyCode))
        {
            Unify.SetError("单位编码不能为空");
            return false;
        }

        if (string.IsNullOrEmpty(input.ClusCode))
        {
            Unify.SetError("套餐编码不能为空");
            return false;
        }

        // 2. 日期格式验证
        if (!DateTime.TryParse(input.Birthday, out _))
        {
            Unify.SetError("出生日期格式不正确");
            return false;
        }

        if (!DateTime.TryParse(input.BeginTime, out var beginTime))
        {
            Unify.SetError("体检时间格式不正确");
            return false;
        }

        // 3. 体检时间不能是过去时间
        if (beginTime.Date < DateTime.Now.Date)
        {
            Unify.SetError("体检时间不能是过去时间");
            return false;
        }

        // 4. 验证套餐是否存在
        var cluster = await Context.Queryable<CodeCluster>()
            .Where(x => x.ClusCode == input.ClusCode && x.IsEnabled == "Y")
            .FirstAsync();

        if (cluster == null)
        {
            Unify.SetError("套餐不存在或已停用");
            return false;
        }

        // 5. 验证价格是否匹配
        if (input.TotalPrice != cluster.Price)
        {
            Unify.SetError("订单价格与套餐价格不匹配");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 处理超时订单
    /// </summary>
    /// <returns></returns>
    public async Task TimeOutOrder()
    {
        var nowTime = DateTime.Now;

        var orders = Context.Queryable<PersonOrder>().Where(x => x.Status == OrderStatus.待支付).ToList();
        foreach (var order in orders)
        {
            // 先查询是否已经支付
            var result = await GetPhysicalExamPayStatus(order);
            if (result.ResultCode != 1)
            {
                order.Status = OrderStatus.异常订单;
                order.ErrorMsg = "查询订单失败";
                await Context.Updateable(order).ExecuteCommandAsync();
                continue;
            }

            if (result.PayFlag)
            {
                order.Status = OrderStatus.已支付;
                await Context.Updateable(order).ExecuteCommandAsync();
                continue;
            }

            TimeSpan ts = nowTime - order.CreateTime;
            if (ts.TotalMinutes < 15)
                continue;

            order.Status = OrderStatus.已超时;
            order.EndTime = DateTime.Now;
            await Context.Updateable(order).ExecuteCommandAsync();

            // 恢复号源、删除体检记录
            await _numberSourceService.UpdatePersonNumberSource(order.BeginTime, order.SourceTypeID, order.TimeSlotID);
            await DeletePhysicalExamOrder(order);
        }
    }

    /// <summary>
    /// 同步订单
    /// </summary>
    /// <returns></returns>
    public async Task SyncOrder()
    {
        var syncDate = DateTime.Now.Date.AddDays(App.Get<int>("SyncDay"));
        var targetStatus = App.Get<bool>("isUsePay") ? OrderStatus.已支付 : OrderStatus.已预约;

        await SyncPersonOrder();
        await SyncTeamOrder();

        #region 内部方法(同步个检订单及团体订单)
        async Task SyncPersonOrder()
        {
            var orderList = await Context.Queryable<PersonOrder>().Where(x => x.Status == targetStatus && x.BeginTime == syncDate).ToListAsync();
            if (orderList.Count == 0)
                return;

            var detailsDict = Context.Queryable<OrderDetail>().Where(x => SqlFunc.ContainsArray(orderList.Select(y => y.Id).ToArray(), x.OrderId))
                              .ToList()
                              .GroupBy(x => x.OrderId)
                              .ToDictionary(y => y.Key, y => y.ToList());

            var successOrders = new List<PersonOrder>();
            foreach (var order in orderList)
            {
                // 从字典获取明细
                detailsDict.TryGetValue(order.Id, out var orderDetails);

                if (!await _syncService.SyncPersonOrder(order, orderDetails))
                    continue;

                order.Status = OrderStatus.已导入;
                successOrders.Add(order);
            }

            if (successOrders.Count == 0)
                return;

            // 只更新订单状态
            await Context.Updateable(successOrders).UpdateColumns(o => new { o.Status }).ExecuteCommandAsync();
        }

        async Task SyncTeamOrder()
        {
            var orderList = await Context.Queryable<TeamOrder>().Where(x => x.Status == targetStatus && x.BeginTime == syncDate).ToListAsync();
            if (orderList.Count == 0)
                return;

            var detailsDict = Context.Queryable<OrderDetail>().Where(x => SqlFunc.ContainsArray(orderList.Select(y => y.Id).ToArray(), x.OrderId))
                              .ToList()
                              .GroupBy(x => x.OrderId)
                              .ToDictionary(y => y.Key, y => y.ToList());

            var successOrders = new List<TeamOrder>();
            foreach (var order in orderList)
            {
                // 从字典获取明细
                detailsDict.TryGetValue(order.Id, out var orderDetails);

                if (!await _syncService.SyncTeamOrder(order, orderDetails))
                    continue;

                order.Status = OrderStatus.已导入;
                successOrders.Add(order);
            }

            if (successOrders.Count == 0)
                return;

            // 只更新订单状态
            await Context.Updateable(successOrders).UpdateColumns(o => new { o.Status }).ExecuteCommandAsync();
        }

        #endregion
    }

    /// <summary>
    /// 获取个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<OrderList>> GetPersonOrderList(PersonOrderPageInput input)
    {
        var queryable = Context.Queryable<PersonOrder>()
            .WhereIF(string.IsNullOrEmpty(input.Name), x => x.Name.Contains(input.Name))
            .WhereIF(Enum.IsDefined(input.OrderStatus), x => x.Status == input.OrderStatus)
            .WhereIF(input.PeClsCode != "0", x => x.PeClsCode == input.PeClsCode)
            .Where(x => SqlFunc.Between(x.BeginTime, input.StartDate, input.EndDate))
            .Select<OrderList>();

        var pageInfo = await queryable.ToPagedListAsync(input.pageNum, input.pageSize); //分页
        return pageInfo;
    }


    /// <summary>
    /// 获取团检订单（分页）
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<TeamOrder>> GetTeamOrderList(TeamOrderPageQueryInput query)
    {
        Expression<Func<TeamOrder, bool>> exp = Expressionable.Create<TeamOrder>() //创建表达式
        .AndIF(query.Status >= 0, it => it.Status == query.Status)
        .AndIF(query.CompanyCode != null, it => it.CompanyCode == query.CompanyCode)
        .AndIF(query.CompanyTimes > 0, it => it.CompanyTimes == query.CompanyTimes)
        .AndIF(query.Name != null, it => it.Name.Contains(query.Name))
        .And(it => it.BeginTime >= query.StartTime && it.BeginTime <= query.EndTime)
        .ToExpression();

        RefAsync<int> total = 0;
        return await Context.Queryable<TeamOrder>().Where(exp).ToPagedListAsync(query.pageNum, query.pageSize);

    }

    #region 私有方法
    /// <summary>
    /// 预约前检查数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<bool> CheckAddPersonOrderData(PersonOrderInput input)
    {
        // 检查组合的数据是否为空
        if (input.OrderDetail.Count == 0)
        {
            Unify.SetError("预约的组合数据不能为空,请检查后再试!");
            return false;
        }

        // 查询预约时段有无号源
        var source = await Context.Queryable<NS_IndividualSource>()
                    .Where(x => x.Date == input.BeginTime && x.SourceTypeID == input.SourceTypeID && x.TimeSlotID == input.TimeSlotID)
                    .FirstAsync();

        if (source == null || source.AvailableCapacity <= 0)
        {
            Unify.SetError("暂无号源！请留意日期号源数");
            return false;
        }

        // 查询有无未缴费的订单，如果有则需要缴费后才能继续预约
        var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.已支付, OrderStatus.正在缴费, OrderStatus.已导入 };
        if (Context.Queryable<PersonOrder>().Any(x => x.CardNo == input.CardNo && x.BeginTime.Date > DateTime.Now.Date && SqlFunc.ContainsArray(statusArray, x.Status)))
        {
            Unify.SetError("已存在体检预约！请先完成已约的订单或取消重新预约");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 添加个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<bool> AddPersonOrder(PersonOrderInput input)
    {
        try
        {
            var order = input.Adapt<PersonOrder>();
            order.Id = IDUtils.GetId();
            order.Status = OrderStatus.待支付;
            order.CreateTime = DateTime.Now;
            order.IsVip = order.TotalPrice > 3000;// 总价大于3000为vip,规则可以改

            var orderDetail = input.OrderDetail.Adapt<List<OrderDetail>>();
            orderDetail.ForEach(x => x.OrderId = order.Id);

            // 添加订单及详情
            await Context.Insertable(order).ExecuteCommandAsync();
            await Context.Insertable(orderDetail).ExecuteCommandAsync();

            // 同步订单到体检系统
            if (!await _syncService.SyncPersonOrder(order, orderDetail))
            {
                Unify.SetError("同步订单失败!");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Unify.SetError("添加订单失败,请联系工作人员!");
            _logger.LogError(ex, "AddPersonOrder参数：{@message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 获取个检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPersonOrder(string memberId)
    {
        return await Context.Queryable<PersonOrder>()
            .Where(x => x.MembersId == memberId)
            .OrderByDescending(x => x.BeginTime)
            .Select<OrderList>()
            .ToListAsync();
    }

    /// <summary>
    /// 获取团检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetGroupOrder(string memberId)
    {
        return await Context.Queryable<TeamOrder>()
           .Where(x => x.MembersId == memberId)
           .OrderByDescending(x => x.BeginTime)
           .Select<OrderList>()
           .ToListAsync();
    }

    /// <summary>
    /// 获取缴费订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPayRecord(string memberId)
    {
        return await Context.Queryable<PayRecord>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }

    /// <summary>
    /// 查询体检系统支付状态
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    private async Task<PayResult> GetPhysicalExamPayStatus(PersonOrder order)
    {
        var data = await _physicalExamService.GetOrderPayStatus(JsonConvert.SerializeObject(new { order.RegNo }));
        if (!data.success)
            return CommonResult();

        var result = JsonConvert.DeserializeObject<PayResult>(data.returnData.ToJson());
        if (result.ResultCode != 1)
            return CommonResult();

        return CommonResult(1, result.PayFlag);

        #region 内部方法
        PayResult CommonResult(int resultCode = -1, bool payFlag = false)
        {
            return new PayResult
            {
                ResultCode = resultCode,
                PayFlag = payFlag
            };
        }
        #endregion
    }

    /// <summary>
    /// 删除体检系统记录
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    private async Task<bool> DeletePhysicalExamOrder(PersonOrder order)
    {
        var deletData = await _physicalExamService.DeleteOrder(JsonConvert.SerializeObject(new { order.RegNo }));
        if (deletData.success)
            return true;

        Unify.SetError("删除体检系统记录失败");
        order.ErrorMsg = deletData.returnMsg;
        order.Status = OrderStatus.异常订单;
        await Context.Updateable(order).ExecuteCommandAsync();
        return false;
    }


    #endregion
}
