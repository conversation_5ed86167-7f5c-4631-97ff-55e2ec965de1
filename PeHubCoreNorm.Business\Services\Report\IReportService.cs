﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 报告服务接口
/// </summary>
public interface IReportService : ITransient
{
    /// <summary>
    /// 获取报告列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<ReportOutput>> GetReportList(ReportInput input);

    /// <summary>
    /// 获取报告详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ReportDetailOutput> GetReportDetail(ReportDetailInput input);
}
