﻿using Newtonsoft.Json.Linq;
using PeHubCoreNorm.Services.PhysicalExamService;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 报告服务
/// </summary>
public class ReportService : IReportService
{
    private readonly ILogger<PhysicalExamService> _logger;
    private readonly IPhysicalExamService _physicalExamService;

    public ReportService(
        ILogger<PhysicalExamService> logger,
        IPhysicalExamService physicalExamService)
    {
        _logger = logger;
        _physicalExamService = physicalExamService;
    }

    /// <summary>
    /// 获取报告列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<ReportOutput>> GetReportList(ReportInput input)
    {
        var reqData = JsonConvert.SerializeObject(input);

        var result = await _physicalExamService.GetReportList(reqData);
        if (!result.success || result.returnData == null)
        {
            Unify.SetError(result.returnMsg);
            return [];
        }

        return JsonConvert.DeserializeObject<List<ReportOutput>>(result.returnData.ToJson());
    }

    /// <summary>
    /// 获取报告列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<ReportDetailOutput> GetReportDetail(ReportDetailInput input)
    {
        var reqData = JsonConvert.SerializeObject(input);

        var result = await _physicalExamService.GetReportDetail(reqData);
        if (!result.success || result.returnData == null)
        {
            Unify.SetError(result.returnMsg);
            return new ReportDetailOutput();
        }

        return JsonConvert.DeserializeObject<ReportDetailOutput>(result.returnData.ToString());
    }



}
