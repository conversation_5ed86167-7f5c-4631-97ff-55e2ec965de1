﻿using PeHubCoreNorm.Services.PhysicalExamService;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 与体检相关的业务
/// </summary>
public class SyncService : BizDbRepository<Company>, ISyncService
{
    private readonly ILogger<PhysicalExamService> _logger;
    private readonly IPhysicalExamService _physicalExamService;

    public SyncService(
        ILogger<PhysicalExamService> logger,
        IPhysicalExamService physicalExamService)
    {
        _logger = logger;
        _physicalExamService = physicalExamService;
    }

    #region 同步体检单位业务
    /// <summary>
    /// 同步体检单位业务
    /// </summary>
    /// <returns></returns>
    public async Task<bool> SyncCompany()
    {
        try
        {
			var result = await _physicalExamService.GetPeCompany();
            if (!result.success)
            {
                Unify.SetError(result.returnMsg);
                return false;
            }

            var remoteData = JsonConvert.DeserializeObject<List<Company>>(result.returnData.ToJson());
            if (remoteData.Count == 0)
            {
                Unify.SetError("同步失败,体检暂无数据");
                return false;
            }

            var peCompanyDic = remoteData.ToDictionary(x => x.CompanyCode.Trim(), x => x);// 体检单位数据
            var localCompanyList = Context.Queryable<Company>().ToList();// 本地单位数据

            // 更新的数据
            var updateList = localCompanyList
                .Where(local => peCompanyDic.TryGetValue(local.CompanyCode, out var peCompany)
                    && local.CompanyName != peCompany.CompanyName)
                .Select(local =>
                {
                    local.CompanyName = peCompanyDic[local.CompanyCode].CompanyName;
                    return local;
                })
                .ToList();
            // 插入的数据
            var insertList = peCompanyDic
                .Where(kv => !localCompanyList.Any(local =>
                    string.Equals(local.CompanyCode, kv.Key)))
                .Select(kv => new Company
                {
                    CompanyCode = kv.Key,
                    CompanyName = kv.Value.CompanyName.Trim(),
                    Status = "Y"
                })
                .ToList();
            if (insertList.Count > 0)
                await Context.Insertable(insertList).ExecuteCommandAsync();

            if (updateList.Count > 0)
                await Context.Updateable(updateList).ExecuteCommandAsync();

            // 同步单位体检次数
            await SyncCompanyTimes();
            return true;
        }
        catch (Exception ex)
        {
            Unify.SetError("同步单位信息异常", ex.Message);
            return false;

		}
    }

    /// <summary>
    /// 同步单位体检次数信息
    /// </summary>
    /// <returns></returns>
    public async Task<bool> SyncCompanyTimes()
    {
        try
        {
            var result = await _physicalExamService.GetPeCompanyTimes();
            if (!result.success)
            {
				Unify.SetError(result.returnMsg);
				return false;
            }

            var resultData = JsonConvert.DeserializeObject<List<CompanyTime>>(result.returnData.ToJson());
            if (resultData.Count == 0)
            {
                Unify.SetError("同步失败,体检暂无数据");
                return false;
            }

            var localData = Context.Queryable<CompanyTime>().ToList();// 本地单位体检次数数据

            // 插入的数据
            var insertList = resultData
                .Where(kv => !localData.Any(local => local.CompanyCode == kv.CompanyCode && local.CompanyTimes == kv.CompanyTimes))
                .Select(kv => kv)
                .ToList();
            if (insertList.Count < 0)
            {
				Unify.SetError("暂无更新的数据");
				return false;
            }

            return await Context.Insertable(insertList).ExecuteCommandAsync() > 0;
        }
        catch (Exception ex)
        {
            Unify.SetError("同步单位体检次数信息", ex.Message);
            return false;

		}
    }

    /// <summary>
    /// 同步单位套餐信息
    /// </summary>
    /// <returns></returns>
    public async Task<bool> SyncCompanyCluster()
    {
        try
        {
            var result = await _physicalExamService.GetPeCompanyCluster();
            if (!result.success)
            {
				Unify.SetError(result.returnMsg);
				return false;
            }

            var insertData = JsonConvert.DeserializeObject<List<CompanyCluster>>(result.returnData.ToJson());
            if (insertData.Count == 0)
            {
                Unify.SetError("同步失败,体检暂无数据");
                return false;
            }

            await Context.Deleteable<CompanyCluster>().ExecuteCommandAsync();
            //同步单位套餐对应的组合
            await SyncCompanyClusterComb();
            return await Context.Insertable(insertData).ExecuteCommandAsync() > 0;
        }
        catch (Exception ex)
        {
            Unify.SetError("同步单位套餐信息", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 同步单位套餐的组合对应信息
    /// </summary>
    /// <returns></returns>
    public async Task<bool> SyncCompanyClusterComb()
    {
        try
        {
            var result = await _physicalExamService.GetPeCompanyClusterComb();
            if (!result.success)
            {
                Unify.SetError(result.returnMsg);
                return false;
            }
 
            var insertData = JsonConvert.DeserializeObject<List<MapCompanyClusterComb>>(result.returnData.ToJson());
            if (insertData.Count == 0)
            {
                Unify.SetError("同步失败,体检暂无数据");
                return false;
            }

            await Context.Deleteable<MapCompanyClusterComb>().ExecuteCommandAsync();
            return await Context.Insertable(insertData).ExecuteCommandAsync() > 0;
        }
        catch (Exception ex)
        {
            Unify.SetError("同步单位套餐的组合对应信息", ex.Message);
            return false;
		}
    }
    #endregion

    #region 同步个检套餐及对应组合
    /// <summary>
    /// 同步个检套餐及对应组合
    /// </summary>
    /// <returns></returns>
    public async Task<bool> SyncClusterAndCombs()
    {
        try
        {
            var result = await _physicalExamService.GetClusterAndCombs();
            if (!result.success)
                return Unify.SetError(result.returnMsg);

            var remoteData = JsonConvert.DeserializeObject<List<PeClusterCombsDto>>(result.returnData.ToJson());
            if (remoteData == null || remoteData.Count == 0)
                return Unify.SetError("同步失败,体检暂无套餐数据");

            var clusters = new List<CodeCluster>();
            var clusterCombs = new List<MapClusterComb>();

            foreach (var item in remoteData)
            {
                clusters.Add(new CodeCluster
                {
                    ClusCode = item.ClusCode,
                    ClusName = item.ClusName,
                    Price = item.Price,
                    Gender = item.Sex.ToString(),
                    IsEnabled = "Y",
                    PeCls = item.PeCls.ToString(),
                    AddRule = "1",
                    IsFixed = "Y",
                    IsAgeLimit = "N",
                    LowerAgeLimit = item.LowerAgeLimit,
                    UpperAgeLimit = item.UpperAgeLimit,
                    Description = item.Description,
                    Notice = item.Notice,
                    Tag = ""
                });

                clusterCombs.Add(new MapClusterComb
                {
                    ClusCode = item.ClusCode,
                    CombCode = item.CombCode,
                    Price = item.CombPrice
                });
            }

            // 先清理数据
            var clusCodeArray = clusters.Select(x => x.ClusCode).ToArray();
            await Context.Deleteable<CodeCluster>().Where(x => x.IsFixed == "Y").ExecuteCommandAsync();
            await Context.Deleteable<MapClusterComb>().Where(x => SqlFunc.ContainsArray(clusCodeArray, x.ClusCode)).ExecuteCommandAsync();

            clusters = clusters.DistinctBy(x => x.ClusCode).ToList();
            clusterCombs = clusterCombs.DistinctBy(x => new { x.ClusCode, x.CombCode }).ToList();

            if (clusters.Count > 0)
                await Context.Insertable(clusters).ExecuteCommandAsync();

            if (clusterCombs.Count > 0)
                await Context.Insertable(clusterCombs).ExecuteCommandAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("SyncClusterAndCombs:{0}", ex.Message);
            return Unify.SetError("同步个检套餐及对应组合异常", ex.Message);
        }
    }
    #endregion

    #region 同步体检组合
    /// <summary>
    /// 同步体检组合
    /// </summary>
    /// <returns></returns>
    public async Task<bool> SyncCodeItemComb()
    {
        try
        {
            var result = await _physicalExamService.GetCodeItemComb();
            if (!result.success)
            {

				Unify.SetError(result.returnMsg);
				return false;
            }

            var remoteData = JsonConvert.DeserializeObject<List<PeCombsDto>>(result.returnData.ToJson());
            if (remoteData.Count == 0)
            {
               Unify.SetError("同步失败,体检暂无组合数据");
                return false;
			}

            var newCombsData = remoteData.Select(x => new CodeItemComb
            {
                CombCode = x.CombCode,
                CombName = x.CombName,
                Gender = x.Sex switch { 0 => "0", 1 => "1", 2 => "2", _ => "0" },
                Price = x.Price,
                Status = "Y",
                IsPriority = false,
                ClsCode = x.ClsCode,
                Attention = x.Hint,
                Description = x.Note
            }).ToList();

            var peCombsDic = newCombsData.ToDictionary(x => x.CombCode, x => x);// 体检组合数据
            var localCombsList = Context.Queryable<CodeItemComb>().ToList();// 本地组合数据

            var updateList = new List<CodeItemComb>();
            foreach (var localComb in localCombsList)
            {
                if (peCombsDic.TryGetValue(localComb.CombCode, out var peComb))
                {
                    if (localComb.CombName != peComb.CombName || localComb.Price != peComb.Price)
                    {
                        localComb.CombName = peComb.CombName;
                        localComb.Price = peComb.Price;
                        updateList.Add(localComb);
                    }
                }
                peCombsDic.Remove(localComb.CombCode);
            }

            // 插入的数据
            var insertList = peCombsDic.Select(x => x.Value).ToList();
            if (insertList.Count > 0)
                await Context.Insertable(insertList).ExecuteCommandAsync();

            if (updateList.Count > 0)
                await Context.Updateable(updateList).ExecuteCommandAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("SyncCodeItemComb:{0}", ex.Message);
            Unify.SetError("同步组合信息异常", ex.Message);
            return false;

		}
    }
    #endregion

    #region 同步体检项目分类
    /// <summary>
    /// 同步体检项目分类
    /// </summary>
    /// <returns></returns>
    public async Task<bool> SyncCodeItemCls()
    {
        try
        {
            var result = await _physicalExamService.GetCodeItemCls();
            if (!result.success)
                return Unify.SetError(result.returnMsg);

            var remoteData = JsonConvert.DeserializeObject<List<CodeItemCls>>(result.returnData.ToJson());
            if (remoteData.Count == 0)
                return Unify.SetError("同步失败,体检暂无数据");

            var peItemClsDic = remoteData.ToDictionary(x => x.ClsCode, x => x);// 体检项目分类数据
            var localItemCls = Context.Queryable<CodeItemCls>().ToList();// 本地项目分类数据

            // 更新的数据
            var updateList = localItemCls
                .Where(local => peItemClsDic.TryGetValue(local.ClsCode, out var peCls)
                    && local.ClsName != peCls.ClsName)
                .Select(local =>
                {
                    local.ClsName = peItemClsDic[local.ClsCode].ClsName;
                    return local;
                })
                .ToList();
            // 插入的数据
            var insertList = peItemClsDic
                .Where(kv => !localItemCls.Any(local =>
                    string.Equals(local.ClsCode, kv.Key)))
                .Select(kv => new CodeItemCls
                {
                    ClsCode = kv.Key,
                    ClsName = kv.Value.ClsName,
                    ShortName = kv.Value.ClsName
                })
                .ToList();

            if (insertList.Count > 0)
                await Context.Insertable(insertList).ExecuteCommandAsync();

            if (updateList.Count > 0)
                await Context.Updateable(updateList).ExecuteCommandAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("SyncCodeItemCls:{0}", ex.Message);
            return Unify.SetError("同步项目分类信息异常", ex.Message);
        }
    }
    #endregion

    #region 同步订单到体检系统
    /// <summary>
    /// 同步个检订单
    /// </summary>
    /// <param name="order"></param>
    /// <param name="orderDetail"></param>
    /// <returns></returns>
    public async Task<bool> SyncPersonOrder(PersonOrder order, List<OrderDetail> orderDetail)
    {
        var requestData = JsonConvert.SerializeObject(new
        {
            Name = order.Name,
            Sex = int.Parse(order.Sex),
            Age = GetAgeByBirthday(order.Birthday),
            Birthday = order.Birthday,
            CardNo = order.CardNo,
            CardType = ChangeCardType(order.CardType),
            Tel = order.Tel,
            PeCls = int.Parse(order.PeClsCode),
            BookNo = order.Id,
            BeginTime = order.BeginTime.ToString("yyyy-MM-dd HH:mm:ss"),
            ClusCode = order.ClusCode,
            IsCompanyCheck = false,
            HospCode = order.AreaCode,
            OperatorCode = order.OperatorCode,
            OperatorName = order.OperatorName,
            orderDetail = orderDetail.Select(x => new
            {
                x.CombCode,
                x.CombName,
                x.CombType
            }).ToList()
        });

        var data = await _physicalExamService.SyncOrderToPeSystem(requestData);
        if (!data.success)
        {
            order.ErrorMsg = data.returnMsg;
            order.Status = OrderStatus.异常订单;
            await Context.Updateable(order).ExecuteCommandAsync();
            return false;
        }

        order.RegNo = data.returnData.ToString();
        return await Context.Updateable(order).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 同步团检订单
    /// </summary>
    /// <param name="order"></param>
    /// <param name="orderDetail"></param>
    /// <returns></returns>
    public async Task<bool> SyncTeamOrder(TeamOrder order, List<OrderDetail> orderDetail)
    {
        var requestData = JsonConvert.SerializeObject(new
        {
            Name = order.Name,
            Sex = int.Parse(order.Sex),
            Age = GetAgeByBirthday(order.Birthday),
            Birthday = order.Birthday,
            CardNo = order.CardNo,
            CardType =  ChangeCardType(order.CardType),
            Tel = order.Tel,
            PeCls = int.Parse(order.PeClsCode),
            BookNo = order.Id,
            BeginTime = order.BeginTime.ToString("yyyy-MM-dd HH:mm:ss"),
            ClusCode = order.ClusCode,
            IsCompanyCheck = true,
            HospCode = order.AreaCode,
            OperatorCode = order.OperatorCode,
            OperatorName = order.OperatorName,
            orderDetail = orderDetail.Select(x => new
            {
                x.CombCode,
                x.CombName,
                x.CombType
            }).ToList()
        });

        var data = await _physicalExamService.SyncOrderToPeSystem(requestData);
        if (!data.success)
        {
            order.ErrorMsg = data.returnMsg;
            order.Status = OrderStatus.异常订单;
            await Context.Updateable(order).ExecuteCommandAsync();
            return false;
        }

        order.RegNo = data.returnData.ToString();
        return await Context.Updateable(order).ExecuteCommandAsync() > 0;
    }
    #endregion

    #region 私有方法
    /// <summary>
    /// 通过出生日期计算年龄
    /// </summary>
    /// <param name="birthday"></param>
    /// <returns></returns>
    public static int GetAgeByBirthday(DateTime birthday)
    {
        DateTime now = DateTime.Now;
        int age = now.Year - birthday.Year;
        if (now.Month < birthday.Month || (now.Month == birthday.Month && now.Day < birthday.Day))
            age--;

        return age < 0 ? 0 : age;
    }

    /// <summary>
    /// 微信证件类型转为体检证件类型
    /// </summary>
    /// <param name="cardType"></param>
    /// <returns></returns>
    public static int ChangeCardType(string cardType)
    {
        // 微信证件类型：01:居民身份证, 02:居民户口簿, 03:护照, 04:军官证, 05:驾驶证, 06:港澳居民来往内地通行证, 07:台湾居民来往内地通行证, 99:其他法定有效证件
        // 体检证件类型：1:居民身份证, 2:护照, 3:港澳通行证, 4:台湾通行证, 5:驾驶证,  6:军官证
        return cardType switch
        {
            "01" => 1,
            "02" => 1,
            "03" => 2,
            "04" => 6,
            "05" => 5,
            "06" => 3,
            "07" => 4,
            "99" => 1,
            _ => 1
        };
    }
    #endregion
}