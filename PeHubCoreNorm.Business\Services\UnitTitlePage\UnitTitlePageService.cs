using PeHubCoreNorm.Business.Entities;
using System.Collections.Generic;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位封面业务实现
/// </summary>
public class UnitTitlePageService : BizDbRepository<UnitTitlePage>, IUnitTitlePageService
{
    private readonly ILogger<UnitTitlePageService> _logger;

    public UnitTitlePageService(ILogger<UnitTitlePageService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 添加单位封面
    /// </summary>
    /// <param name="input">添加参数</param>
    /// <returns></returns>
    public async Task<bool> AddUnitTitlePage(UnitTitlePageAddInput input)
    {
        try
        {

             
            // 验证Base64格式并转换为字节数组
            //byte[] imageBytes;
            //try
            //{
            //    imageBytes = Utils.ImageUtils.ConvertBase64ToBytes(input.ImgData);
            //}
            //catch (ArgumentException ex)
            //{
            //    Unify.SetError($"图片数据格式错误: {ex.Message}");
            //    return false;
            //}

            //// 验证图片格式
            //if (!Utils.ImageUtils.IsSupportedImageFormat(imageBytes))
            //{
            //    Unify.SetError("不支持的图片格式，请使用JPEG、PNG、GIF或WebP格式!");
            //    return false;
            //}

            //// 验证图片大小
            //if (!Utils.ImageUtils.IsValidImageSize(imageBytes))
            //{
            //    Unify.SetError("图片大小超出限制，最大支持5MB!");
            //    return false;
            //}

            //// 验证文件大小参数
            //if (input.FileSize != imageBytes.Length)
            //{
            //    Unify.SetError("文件大小与实际数据大小不匹配!");
            //    return false;
            //}

            // 创建实体
            var entity = input.Adapt<UnitTitlePage>();
            entity.Id= input.Id;
         
            await Context.Storageable(entity).DefaultAddElseUpdate().ExecuteCommandAsync();



            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"添加单位封面异常，单位编码:{input.CompanyCode}，体检次数:{input.CompanyTimes}");
            Unify.SetError("添加单位封面失败，请稍后重试!");
            return false;
        }
    }

    /// <summary>
    /// 检查单位封面是否存在
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">体检次数</param>
    /// <param name="excludeId">排除的ID（编辑时使用）</param>
    /// <returns></returns>
    public async Task<bool> CheckUnitTitlePageExists(string companyCode, int companyTimes, string excludeId = null)
    {
        return await Context.Queryable<UnitTitlePage>()
            .Where(it => it.CompanyCode == companyCode && it.CompanyTimes == companyTimes)
            .WhereIF(!string.IsNullOrEmpty(excludeId), it => it.Id != excludeId)
            .AnyAsync();
    }

    /// <summary>
    /// 根据ID获取单位封面详情
    /// </summary>
    /// <param name="id">主键ID</param>
    /// <returns></returns>
    public async Task<UnitTitlePageOutput> GetUnitTitlePageById(string id)
    {
        return await Context.Queryable<UnitTitlePage>()
            .Where(it => it.Id == id)
            .Select<UnitTitlePageOutput>()
            .FirstAsync();
    }

    /// <summary>
    /// 根据单位编码和体检次数获取单位封面
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">体检次数</param>
    /// <returns></returns>
    public async Task<UnitTitlePageOutput> GetUnitTitlePageByCompany(string companyCode, int companyTimes)
    {
        return await Context.Queryable<UnitTitlePage>()
            .Where(it => it.CompanyCode == companyCode && it.CompanyTimes == companyTimes)
            .Select<UnitTitlePageOutput>()
            .FirstAsync();
    }
}
