﻿namespace PeHubCoreNorm.RBAC;

[ApiExplorerSettings(GroupName = "RBAC")]
[Route("sys/[controller]")]
public class OrgController : BaseControllerRoleAuthorize
{
    private readonly ISysOrgService _orgService;

    public OrgController(ISysOrgService orgService)
    {
		_orgService = orgService;
    }

    /// <summary>
    /// 组织机构分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("page")]
    [ActionPermission(ActionType.Query, "组织机构分页查询", "组织机构管理")]
    public async Task<dynamic> Page([FromQuery] OrgPageInput input)
    {
        return await _orgService.Page(input);
    }

	/// <summary>
	/// 组织机构详情
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpGet("detail")]
    [ActionPermission(ActionType.Query, "组织机构详情", "组织机构管理")]
    public async Task<dynamic> Detail([FromQuery] BaseIdInput input)
    {
        return await _orgService.GetSysOrgById(input.Id);
    }

	/// <summary>
	/// 添加组织机构
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("add")]
    [ActionPermission(ActionType.Button, "添加组织机构", "组织机构管理")]
    public async Task Add([FromBody] OrgAddInput input)
    {
        await _orgService.Add(input);
    }

	/// <summary>
	/// 修改组织机构
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("edit")]
    [ActionPermission(ActionType.Button, "修改组织机构", "组织机构管理")]
    public async Task Edit([FromBody] OrgEditInput input)
    {
        await _orgService.Edit(input);
    }

	/// <summary>
	/// 删除组织机构
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("delete")]
    [ActionPermission(ActionType.Button, "删除组织机构", "组织机构管理")]
    public async Task Delete([FromBody] BaseIdsInput input)
    {
        await _orgService.Delete(input);
    }

	/// <summary>
	/// 修改Logo
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("updateAvatar")]
	[ActionPermission(ActionType.Button, "修改Logo", "组织机构管理")]
	public async Task<dynamic> UpdateAvatar([FromForm] BaseFileInput input)
	{
		return await _orgService.UpdateAvatar(input);
	}
}