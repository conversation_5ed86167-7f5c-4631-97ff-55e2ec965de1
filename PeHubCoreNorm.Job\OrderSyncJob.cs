﻿using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PeHubCoreNorm.Business;

namespace PeHubCoreNorm.Job
{
	public class OrderSyncJob : BackgroundService, ITransient
	{
		private readonly ILogger<OrderSyncJob> _logger;

		public OrderSyncJob(ILogger<OrderSyncJob> logger)
		{
			_logger = logger;
		}

		public async Task MyBackgroundJobMethod()
		{
			UserManager.setAreaCodeValue("A01");
			var _orderService = App.ServiceProvider.GetService<IOrderService>();
			await _orderService.SyncOrder();
		}


		protected override async Task ExecuteAsync(CancellationToken stoppingToken)
		{
			try
			{
				if (!App.Get<bool>("Hangfire:enabled"))
					return;

                RecurringJob.AddOrUpdate("syncOrder_A01", () => MyBackgroundJobMethod(), "0 0 17 * * ? ");
                //RecurringJob.AddOrUpdate("syncOrder_A01", () => MyBackgroundJobMethod(), "0 */5 * * * ? ");

                // 保持服务运行(关键!)
                //await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(Timeout.Infinite, stoppingToken);
                }
            }
			catch (Exception e)
			{
				_logger.LogError(e, "orderSnyc作业报错了");
			}
		}
	}
}
