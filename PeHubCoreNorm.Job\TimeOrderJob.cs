﻿using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PeHubCoreNorm.Business;

namespace PeHubCoreNorm.Job
{
    public class TimeOrderJob : BackgroundService, ITransient
    {
        private readonly ILogger<TimeOrderJob> _logger;

        public TimeOrderJob(ILogger<TimeOrderJob> logger)
        {
            _logger = logger;
        }
        public async Task MyBackgroundJobMethod()
        {
            UserManager.setAreaCodeValue("A01");
            var _orderService = App.ServiceProvider.GetService<IOrderService>();
            await _orderService.TimeOutOrder();
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                if (!App.Get<bool>("Hangfire:enabled"))
                {
                    return;
                }

                RecurringJob.AddOrUpdate("timeOrderJob_A01",() => MyBackgroundJobMethod(), "0 */5 * * * ? ");

                // 保持服务运行(关键!)
                //await Task.Delay(time.FromMinutes(1), stoppingToken);
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(Timeout.Infinite, stoppingToken);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "orderSnyc作业报错了");
            }
        }
    }
}
