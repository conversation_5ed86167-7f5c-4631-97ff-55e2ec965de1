﻿namespace PeHubCoreNorm.Services.PhysicalExamService;

/// <summary>
/// 获取体检业务的接口
/// </summary>
public interface IPhysicalExamService : ITransient
{
    /// <summary>
    /// 获取体检单位信息
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetPeCompany();

    /// <summary>
    /// 获取体检单位次数信息
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetPeCompanyTimes();

    /// <summary>
    /// 获取体检单位套餐信息
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetPeCompanyCluster();

    /// <summary>
    /// 获取体检单位套餐对应的组合信息
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetPeCompanyClusterComb();

    /// <summary>
    /// 获取体检套餐及对应组合
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetClusterAndCombs();

    /// <summary>
    /// 获取体检组合
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetCodeItemComb();

    /// <summary>
    /// 获取体检项目分类
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetCodeItemCls();

    /// <summary>
    /// 获取报告列表
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    Task<ResponseResult> GetReportList(string reqData);

    /// <summary>
    /// 获取报告详情
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    Task<ResponseResult> GetReportDetail(string reqData);

    /// <summary>
    /// 同步订单
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    Task<ResponseResult> SyncOrderToPeSystem(string reqData);

    /// <summary>
    /// 删除订单
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    Task<ResponseResult> DeleteOrder(string reqData);

    /// <summary>
    /// 获取订单支付状态
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    Task<ResponseResult> GetOrderPayStatus(string reqData);
}