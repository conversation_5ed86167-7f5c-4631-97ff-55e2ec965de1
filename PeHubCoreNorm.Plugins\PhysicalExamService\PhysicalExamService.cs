﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Org.BouncyCastle.Ocsp;
using PeHubCoreNorm.Utils;
using PeHubCoreNorm.Utils.RSA;

namespace PeHubCoreNorm.Services.PhysicalExamService;

/// <summary>
/// 获取体检业务的服务
/// </summary>
public class PhysicalExamService : IPhysicalExamService
{
    private readonly ILogger<PhysicalExamService> _logger;
    private readonly IHttpClientHelper _httpClientHelper;
    private static readonly string _PeUrl = App.Get<string>("PeConfig:PeUrl");// 体检接口地址
    private static readonly bool _IsEncrypt = App.Get<bool>("PeConfig:IsEncrypt");// 是否加密

    public PhysicalExamService(
     ILogger<PhysicalExamService> logger,
     IHttpClientHelper httpClientHelper)
    {
        _logger = logger;
        _httpClientHelper = httpClientHelper;
    }

    /// <summary>
    /// 获取体检单位信息
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetPeCompany() => await ExecuteRequest(PeInterfaceName.GetAllCompany, "获取单位信息异常");

    /// <summary>
    /// 获取体检单位次数信息
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetPeCompanyTimes() => await ExecuteRequest(PeInterfaceName.GetAllCompanyTimes, "获取单位体检次数信息异常");

    /// <summary>
    /// 获取体检单位套餐信息
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetPeCompanyCluster() => await ExecuteRequest(PeInterfaceName.GetAllCompanyCluster, "获取体检单位套餐信息失败");

    /// <summary>
    /// 获取体检单位套餐对应的组合信息
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetPeCompanyClusterComb() => await ExecuteRequest(PeInterfaceName.GetAllMapCompanyClusterComb, "获取体检单位套餐的组合对应信息失败");

    /// <summary>
    /// 获取体检套餐及对应组合
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetClusterAndCombs() => await ExecuteRequest(PeInterfaceName.GetClusterAndCombs, "同步获取体检套餐及对应组合异常");

    /// <summary>
    /// 获取体检组合
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetCodeItemComb() => await ExecuteRequest(PeInterfaceName.GetAllComb, "同步组合信息异常");

    /// <summary>
    /// 获取体检项目分类
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetCodeItemCls() => await ExecuteRequest(PeInterfaceName.GetAllItemCls, "同步项目分类信息异常");

    /// <summary>
    /// 获取报告列表
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    public async Task<ResponseResult> GetReportList(string reqData) => await ExecuteRequest(PeInterfaceName.ReportList, "查询报告列表失败", reqData);

    /// <summary>
    /// 获取报告详情
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    public async Task<ResponseResult> GetReportDetail(string reqData) => await ExecuteRequest(PeInterfaceName.ReportDetail, "查询报告详情失败", reqData);

    /// <summary>
    /// 同步订单
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    public async Task<ResponseResult> SyncOrderToPeSystem(string reqData) => await ExecuteRequest(PeInterfaceName.SyncOrder, "同步订单失败", reqData);

    /// <summary>
    /// 删除订单
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    public async Task<ResponseResult> DeleteOrder(string reqData) => await ExecuteRequest(PeInterfaceName.DeleteOrder, "删除订单失败", reqData);

    /// <summary>
    /// 获取订单支付状态
    /// </summary>
    /// <param name="reqData"></param>
    /// <returns></returns>
    public async Task<ResponseResult> GetOrderPayStatus(string reqData) => await ExecuteRequest(PeInterfaceName.GetPayStatus, "获取订单状态失败", reqData);

    #region 私有方法
    /// <summary>
    /// 执行请求
    /// </summary>
    /// <param name="suffixUrl"></param>
    /// <param name="errMsg"></param>
    /// <returns></returns>
    private async Task<ResponseResult> ExecuteRequest(string suffixUrl, string errMsg, string paramData = "")
    {
        try
        {
            var requestUrl = $"{_PeUrl}{suffixUrl}";
            string requestData = string.IsNullOrEmpty(paramData) ? string.Empty : ProcessRequestData(paramData);

            var respData = await _httpClientHelper.PostAsync(requestUrl, UserManager.AreaCode, requestData);
            _logger.LogInformation($"体检接口:{suffixUrl}返参:{respData}");

            var response = JsonConvert.DeserializeObject<ResponseResult>(respData);
            return response ?? ErrorResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError("{MethodName}: {ErrorMessage}", suffixUrl, ex.Message);
            return ErrorResponse($"{errMsg}: {ex.Message}");
        }

        // 内部方法处理请求数据
        string ProcessRequestData(string paramData)
        {
            _logger.LogInformation($"体检接口:{suffixUrl}入参:{paramData}");
            return _IsEncrypt ? JsonConvert.SerializeObject(new { encryStr = RSACryptoHelper.Encrypt(paramData, EncryptConfig.publicKey) }) : paramData;
        }
    }

    /// <summary>
    /// 返回错误信息
    /// </summary>
    /// <param name="msg"></param>
    /// <returns></returns>
    private static ResponseResult ErrorResponse(string msg = "体检返回数据为空")
    {
        return new() { success = false, returnMsg = msg };
    }
    #endregion
}
