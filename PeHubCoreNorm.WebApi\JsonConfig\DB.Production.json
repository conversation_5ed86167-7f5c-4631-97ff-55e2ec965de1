{
  //sqlsugar设置
  "SqlSugarSettings": {
    //Sqlsugar连接字符串配置
    "ConnectionStrings": [
      {
        "ConfigId": "PeHubCoreNorm",
        "ConnectionString": "Data Source=192.168.2.200\\MSSQLSERVER2019,21433;Initial Catalog=PeHubCoreNorm; User ID=sa;Password=****;TrustServerCertificate=true;",
        "DbType": "sqlserver",
        //数据库类型
        "IsAutoCloseConnection": true,
        //是否自动释放
        "IsInitDb": false,
        //初始化数据库
        "IsSeedData": false,
        //初始化种子
        "FromEnvironment": false
        //字符串连接来自环境变量,如果是则取ConnectionString对应的值
      },
      {
        "ConfigId": "A01",
        "ConnectionString": "Data Source=192.168.2.200\\MSSQLSERVER2019,21433;Initial Catalog=PeHubCoreNorm_SD003;User ID=sa;Password=****;TrustServerCertificate=true;",
        "DbType": "sqlserver",
        //数据库类型
        "IsAutoCloseConnection": true,
        //是否自动释放
        "IsInitDb": false,
        //初始化数据库
        "IsSeedData": false,
        //初始化种子
        "FromEnvironment": false
        //字符串连接来自环境变量,如果是则取ConnectionString对应的值
      }
    ]
  },
  "CacheSettings": {
    "UseRedis": false,
    // true使用Redis,否则使用内存缓存
    "RedisSettings": {
      "Host": "127.0.0.1",
      "Port": 6379,
      "Password": "****",
      "Db": 14,
      "TimeOut": 5000
    }
  }
}