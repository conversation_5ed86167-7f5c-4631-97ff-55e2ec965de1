﻿using Newtonsoft.Json;
using System.IO;
using System.Net.Http;

namespace PeHubCoreNorm.Utils;

public interface IHttpClientHelper
{
    Task<string> DeleteAsync(string url, Dictionary<string, string> headers = null, int timeOut = 30);
    Task<T> DeleteAsync<T>(string url, Dictionary<string, string> headers = null, int timeOut = 30) where T : new();
    Task<string> GetAsync(string url, Dictionary<string, string> headers = null, int timeOut = 30);
    Task<T> GetAsync<T>(string url, Dictionary<string, string> headers = null, int timeOut = 30) where T : new();
    Task<string> PostAsync(string url, Dictionary<string, string> body, string bodyMediaType = null, string responseContentType = null, Dictionary<string, string> headers = null, int timeOut = 30);
    Task<string> PostAsync(string url, string hospCode, string data, Dictionary<string, string> headers = null, string bodyMediaType = "application/json", string responseContentType = null, int timeOut = 30);
    /// <summary>
    /// 发起POST异步请求
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="data">POST提交的内容</param>
    /// <returns>返回string</returns>
    Task<string> PostAsync(string url, string hospCode, string data);
    Task<T> PostAsync<T>(string url, Dictionary<string, string> body, string bodyMediaType = null, string responseContentType = null, Dictionary<string, string> headers = null, int timeOut = 30) where T : new();
    Task<string> PutAsync(string url, string body, string bodyMediaType = null, string responseContentType = null, Dictionary<string, string> headers = null, int timeOut = 30);
    Task<T> PutAsync<T>(string url, string body, string bodyMediaType = null, string responseContentType = null, Dictionary<string, string> headers = null, int timeOut = 30) where T : new();
}

/// <summary>
/// HTTP帮助类
/// </summary>
public class HttpClientHelper : IHttpClientHelper
{
    private IHttpClientFactory _httpClientFactory;
    public HttpClientHelper(IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
    }

    /// <summary>
    /// 发起DELETE异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> DeleteAsync(string url, Dictionary<string, string> headers = null, int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using (HttpClient client = _httpClientFactory.CreateClient(hostName))
        {
            client.Timeout = TimeSpan.FromSeconds(timeOut);
            if (headers?.Count > 0)
            {
                foreach (string key in headers.Keys)
                {
                    client.DefaultRequestHeaders.Add(key, headers[key]);
                }
            }
            using (HttpResponseMessage response = await client.DeleteAsync(url))
            {
                if (response.IsSuccessStatusCode)
                {
                    string responseString = await response.Content.ReadAsStringAsync();
                    return responseString;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
    }

    /// <summary>
    /// 发起DELETE异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回T</returns>
    public async Task<T> DeleteAsync<T>(string url, Dictionary<string, string> headers = null, int timeOut = 30) where T : new()
    {
        string responseString = await DeleteAsync(url, headers, timeOut);
        if (!string.IsNullOrWhiteSpace(responseString))
        {
            return JsonConvert.DeserializeObject<T>(responseString);
        }
        else
        {
            return default(T);
        }
    }

    /// <summary>
    /// 发起GET异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> GetAsync(string url, Dictionary<string, string> headers = null, int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using (HttpClient client = _httpClientFactory.CreateClient(hostName))
        {
            client.Timeout = TimeSpan.FromSeconds(timeOut);
            if (headers?.Count > 0)
            {
                foreach (string key in headers.Keys)
                {
                    client.DefaultRequestHeaders.Add(key, headers[key]);
                }
            }
            using (HttpResponseMessage response = await client.GetAsync(url))
            {
                if (response.IsSuccessStatusCode)
                {
                    string responseString = await response.Content.ReadAsStringAsync();
                    return responseString;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
    }

    /// <summary>
    /// 发起GET异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回T</returns>
    public async Task<T> GetAsync<T>(string url, Dictionary<string, string> headers = null, int timeOut = 30) where T : new()
    {
        string responseString = await GetAsync(url, headers, timeOut);
        if (!string.IsNullOrWhiteSpace(responseString))
        {
            return JsonConvert.DeserializeObject<T>(responseString);
        }
        else
        {
            return default(T);
        }
    }

    /// <summary>
    /// 发起POST异步请求
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="body">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> PostAsync(string url, Dictionary<string, string> body,
        string bodyMediaType = null,
        string responseContentType = null,
        Dictionary<string, string> headers = null,
        int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using (HttpClient client = _httpClientFactory.CreateClient(hostName))
        {
            client.Timeout = TimeSpan.FromSeconds(timeOut);
            if (headers?.Count > 0)
            {
                foreach (string key in headers.Keys)
                {
                    client.DefaultRequestHeaders.Add(key, headers[key]);
                }
            }

            try
            {
                var content = new FormUrlEncodedContent(body);
                using (HttpResponseMessage response = await client.PostAsync(url, content))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        string responseString = await response.Content.ReadAsStringAsync();
                        return responseString;
                    }
                    else
                    {
                        return string.Empty;
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
        }
    }

    /// <summary>
    /// 发起POST异步请求
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="data">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> PostAsync(string url, string hospCode, string data,
        Dictionary<string, string> headers = null,
        string bodyMediaType = "application/json",
        string responseContentType = null,
        int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using HttpClient client = _httpClientFactory.CreateClient(hostName);
        client.DefaultRequestHeaders.Add("hospCode", hospCode);
        client.Timeout = TimeSpan.FromSeconds(timeOut);
        if (headers?.Count > 0)
        {
            foreach (string key in headers.Keys)
            {
                client.DefaultRequestHeaders.Add(key, headers[key]);
            }
        }

        try
        {
            HttpContent content = new StringContent(data, Encoding.UTF8, bodyMediaType);
            using HttpResponseMessage response = await client.PostAsync(url, content);
            if (!response.IsSuccessStatusCode)
                return string.Empty;

            string responseString = await response.Content.ReadAsStringAsync();
            return responseString;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }

    /// <summary>
    /// 发起POST异步请求
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="data">POST提交的内容</param>
    /// <returns>返回string</returns>
    public async Task<string> PostAsync(string url, string hospCode, string data)
    {
        var hostName = GetHostName(url);
        using HttpClient client = _httpClientFactory.CreateClient(hostName);
        client.DefaultRequestHeaders.Add("hospCode", hospCode);
        client.Timeout = TimeSpan.FromSeconds(20); // 设置超时时间为20秒
        HttpContent content = new StringContent(data, Encoding.UTF8, "application/json");
        using HttpResponseMessage response = await client.PostAsync(url, content);
        if (response.IsSuccessStatusCode)
        {
            // 默认使用UTF-8编码，除非响应头中指定了不同的字符集
            Encoding encoding = Encoding.UTF8; // 你可以根据需要使用其他编码
            return await response.Content.ReadAsStringAsync();
        }
        else
        {
            // 处理错误响应
            throw new HttpRequestException($"Request to {url} failed with status code {response.StatusCode}");
        }
    }

    /// <summary>
    /// 发起POST异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="body">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回T</returns>
    public async Task<T> PostAsync<T>(string url, Dictionary<string, string> body,
        string bodyMediaType = null,
        string responseContentType = null,
        Dictionary<string, string> headers = null,
        int timeOut = 30) where T : new()
    {
        string responseString = await PostAsync(url, body, bodyMediaType, responseContentType, headers, timeOut);
        if (!string.IsNullOrWhiteSpace(responseString))
        {
            return JsonConvert.DeserializeObject<T>(responseString);
        }
        else
        {
            return default(T);
        }
    }

    /// <summary>
    /// 发起Put异步请求
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="body">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> PutAsync(string url, string body,
        string bodyMediaType = null,
        string responseContentType = null,
        Dictionary<string, string> headers = null,
        int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using (HttpClient client = _httpClientFactory.CreateClient(hostName))
        {
            client.Timeout = TimeSpan.FromSeconds(timeOut);
            if (headers?.Count > 0)
            {
                foreach (string key in headers.Keys)
                {
                    client.DefaultRequestHeaders.Add(key, headers[key]);
                }
            }
            StringContent content = new StringContent(body, Encoding.UTF8, mediaType: bodyMediaType);
            if (!string.IsNullOrWhiteSpace(responseContentType))
            {
                content.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse(responseContentType);
            }
            using (HttpResponseMessage response = await client.PutAsync(url, content))
            {
                if (response.IsSuccessStatusCode)
                {
                    string responseString = await response.Content.ReadAsStringAsync();
                    return responseString;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
    }

    /// <summary>
    /// 发起PUT异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="body">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回T</returns>
    public async Task<T> PutAsync<T>(string url, string body,
        string bodyMediaType = null,
        string responseContentType = null,
        Dictionary<string, string> headers = null,
        int timeOut = 30) where T : new()
    {
        string responseString = await PutAsync(url, body, bodyMediaType, responseContentType, headers, timeOut);
        if (!string.IsNullOrWhiteSpace(responseString))
        {
            return JsonConvert.DeserializeObject<T>(responseString);
        }
        else
        {
            return default(T);
        }
    }
    #region 私有函数
    /// <summary>
    /// 获取请求的主机名
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    private static string GetHostName(string url)
    {
        if (!string.IsNullOrWhiteSpace(url))
        {
            return url.Replace("https://", "").Replace("http://", "").Split('/')[0];
        }
        else
        {
            return "AnyHost";
        }
    }
    #endregion
}
