﻿using System.IO;
using System.Security.Cryptography;

namespace PeHubCoreNorm.Utils.RSA
{
    /// <summary>
    /// RSA加解密
    /// </summary>
    public class RSACryptoHelper
    {
        /// <summary>
        /// 加密 用的是PEM格式的密钥
        /// </summary>
        /// <param name="str_Plain_Text">要加密的数据</param>
        /// <param name="str_Public_PEMKey"></param>
        /// <returns></returns>
        public static string Encrypt(string str_Plain_Text, string str_Public_PEMKey)
        {
            using (RSACryptoServiceProvider RSA = RSA_PEM.FromPEM(str_Public_PEMKey))
            {
                return RSACrypto.Encrypt_PEMKey(str_Plain_Text, RSA);
            }
        }

        /// <summary>
        /// 循环解密
        /// </summary>
        /// <param name="str_Cypher_Text">需要解密的数据</param>
        /// <param name="str_Private_PEMKey">PEMKey密钥</param>
        /// <returns></returns>
        public static string Decrypt(string str_Cypher_Text, string str_Private_PEMKey)
        {
            string[] splitKey = { "-SPLIT-" };
            string[] result = str_Cypher_Text.Split(splitKey, StringSplitOptions.RemoveEmptyEntries);
            string[] decryptArr = new string[result.Length];
            //使用了-SPLIT-分割多份密文
            for (var i = 0; i < result.Length; i++)
            {
                string encryptData = result[i];
                string decryptData = RSACrypto.Decrypt_PEMKey(encryptData, str_Private_PEMKey);
                decryptArr[i] = decryptData;
            }

            string decryptStr = string.Join("", decryptArr);
            //原始字符串使用了base64编码了一次，解密是使用base64转码回去
            string original = Encoding.UTF8.GetString(Convert.FromBase64String(decryptStr));
            return original;
        }
    }

    /// <summary>
    /// RSA加解密实现
    /// </summary>
    public static class RSACrypto
    {
        /// <summary>
        /// 取得私钥和公钥 XML 格式,返回数组第一个是私钥,第二个是公钥.
        /// </summary>
        /// <param name="size">密钥长度,默认1024,可以为2048</param>
        /// <returns></returns>
        public static string[] CreateXmlKey(int size = 1024)
        {
            //密钥格式要生成pkcs#1格式的  而不是pkcs#8格式的
            RSACryptoServiceProvider sp = new RSACryptoServiceProvider(size);
            string privateKey = sp.ToXmlString(true);//private key
            string publicKey = sp.ToXmlString(false);//public  key
            return new string[] { privateKey, publicKey };
        }

        /// <summary>
        /// 取得私钥和公钥 CspBlob 格式,返回数组第一个是私钥,第二个是公钥.
        /// </summary>
        /// <param name="size"></param>
        /// <returns></returns>
        public static string[] CreateCspBlobKey(int size = 1024)
        {
            //密钥格式要生成pkcs#1格式的  而不是pkcs#8格式的
            RSACryptoServiceProvider sp = new RSACryptoServiceProvider(size);
            string privateKey = Convert.ToBase64String(sp.ExportCspBlob(true));//private key
            string publicKey = Convert.ToBase64String(sp.ExportCspBlob(false));//public  key 

            return new string[] { privateKey, publicKey };
        }

        /// <summary>
        /// 导出PEM PKCS#1格式密钥对，返回数组第一个是私钥,第二个是公钥.
        /// </summary>
        public static string[] CreateKey_PEM_PKCS1(int size = 1024)
        {
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(size);
            string privateKey = RSA_PEM.ToPEM(rsa, false, false);
            string publicKey = RSA_PEM.ToPEM(rsa, true, false);
            return new string[] { privateKey, publicKey };
        }

        /// <summary>
        /// 导出PEM PKCS#8格式密钥对，返回数组第一个是私钥,第二个是公钥.
        /// </summary>
        public static string[] CreateKey_PEM_PKCS8(int size = 1024)
        {
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(size);
            string privateKey = RSA_PEM.ToPEM(rsa, false, true);
            string publicKey = RSA_PEM.ToPEM(rsa, true, true);
            return new string[] { privateKey, publicKey };

        }

        /// <summary>
        /// 加密 用的是PEM格式的密钥
        /// </summary>
        /// <param name="str_Plain_Text"></param>
        /// <param name="RSA"></param>
        /// <returns></returns>
        public static string Encrypt_PEMKey(string str_Plain_Text, RSACryptoServiceProvider RSA)
        {
            var data = Convert.ToBase64String(Encoding.UTF8.GetBytes(str_Plain_Text));

            int buffersize = (RSA.KeySize / 8) - 11;
            var buffer = new byte[buffersize];
            using (MemoryStream input = new MemoryStream(Encoding.UTF8.GetBytes(data)), output = new MemoryStream())
            {
                while (true)
                {
                    int readsize = input.Read(buffer, 0, buffersize);
                    if (readsize <= 0)
                    {
                        break;
                    }
                    var temp = new byte[readsize];
                    Array.Copy(buffer, 0, temp, 0, readsize);
                    var EncBytes = RSA.Encrypt(temp, false);
                    output.Write(EncBytes, 0, EncBytes.Length);
                }
                return Convert.ToBase64String(output.ToArray());
            }
        }

        /// <summary>
        /// 解密 用的是PEM格式的密钥
        /// </summary>
        /// <param name="str_Cypher_Text">密文</param>
        /// <param name="str_Private_Key">密钥</param>
        /// <returns></returns>
        public static string Decrypt_PEMKey(string str_Cypher_Text, string str_Private_PEMKey)
        {
            using (var RSA = RSA_PEM.FromPEM(str_Private_PEMKey))
            {
                var data = Convert.FromBase64String(str_Cypher_Text);

                //RSA.FromXmlString(str_Private_Key);
                int buffersize = RSA.KeySize / 8;
                var buffer = new byte[buffersize];
                using (MemoryStream input = new MemoryStream(data), output = new MemoryStream())
                {
                    while (true)
                    {
                        int readsize = input.Read(buffer, 0, buffersize);
                        if (readsize <= 0)
                        {
                            break;
                        }

                        var temp = new byte[readsize];
                        Array.Copy(buffer, 0, temp, 0, readsize);
                        var DecBytes = RSA.Decrypt(temp, false);
                        output.Write(DecBytes, 0, DecBytes.Length);
                    }
                    return Encoding.UTF8.GetString(output.ToArray());
                }
            }
        }
    }
}
